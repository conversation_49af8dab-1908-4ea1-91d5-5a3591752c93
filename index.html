<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>Export Businesses Using Overpass (No Google API)</title>
  <style>
    /* CSS Custom Properties for consistent theming */
    :root {
      --primary-color: #004B8D;
      --secondary-color: #10a37f;
      --danger-color: #d32f2f;
      --success-color: #1976d2;
      --border-color: #ccc;
      --light-border: #e5e5e5;
      --background-light: #f9f9f9;
      --background-lighter: #f5f5f5;
      --text-color: #333;
      --text-muted: #666;
      --text-light: #555;
      --shadow-light: 0 1px 3px rgba(0,0,0,0.1);
      --shadow-medium: 0 4px 12px rgba(0,0,0,0.3);
      --border-radius: 4px;
      --border-radius-large: 8px;
      --spacing-xs: 0.25rem;
      --spacing-sm: 0.5rem;
      --spacing-md: 1rem;
      --spacing-lg: 1.5rem;
      --spacing-xl: 2rem;
    }

    /* Reset and base styles */
    * {
      box-sizing: border-box;
    }

    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Arial, sans-serif;
      margin: 0;
      padding: var(--spacing-sm);
      color: var(--text-color);
      line-height: 1.5;
      background-color: #fff;
    }

    /* Mobile-first responsive container */
    .container {
      max-width: 100%;
      margin: 0 auto;
      padding: 0 var(--spacing-sm);
    }

    /* Branded Header Styles */
    .branded-header {
      background: linear-gradient(135deg, var(--primary-color) 0%, #003a6b 100%);
      color: white;
      padding: var(--spacing-md) var(--spacing-sm);
      margin-bottom: var(--spacing-lg);
      box-shadow: var(--shadow-medium);
    }

    .header-container {
      max-width: 1200px;
      margin: 0 auto;
      display: grid;
      grid-template-columns: 1fr auto 1fr;
      gap: var(--spacing-md);
      align-items: center;
      min-height: 80px;
    }

    .header-nav {
      display: flex;
      flex-direction: column;
      gap: var(--spacing-sm);
      align-items: flex-start;
      justify-self: start;
    }

    .header-brand {
      text-align: center;
      justify-self: center;
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: var(--spacing-xs);
    }

    .brand-icon {
      font-size: 2.5rem;
      margin-bottom: var(--spacing-xs);
    }

    .brand-logo {
      font-size: 1.8rem;
      font-weight: 700;
      margin: 0;
      color: white;
      text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
    }

    .brand-logo a {
      color: white;
      text-decoration: none;
      transition: opacity 0.2s ease;
    }

    .brand-logo a:hover {
      opacity: 0.8;
    }

    .brand-tagline {
      font-size: 0.85rem;
      margin: 0;
      opacity: 0.9;
      font-weight: 300;
      text-align: center;
      line-height: 1.3;
    }

    .header-contact {
      justify-self: end;
      text-align: right;
    }

    .main-nav a {
      color: var(--primary-color);
      text-decoration: none;
      font-weight: 500;
      padding: var(--spacing-sm);
      text-align: center;
      border-radius: var(--border-radius);
      transition: background-color 0.2s ease;
    }

    .main-nav a:hover {
      text-decoration: underline;
    }

    /* Header responsive styles */
    @media screen and (max-width: 599px) {
      .header-container {
        grid-template-columns: 1fr;
        gap: var(--spacing-sm);
        text-align: center;
      }

      .header-nav {
        order: 3;
        flex-direction: row;
        justify-content: center;
        align-items: center;
        justify-self: center;
        gap: var(--spacing-md);
      }

      .header-brand {
        order: 1;
        justify-self: center;
      }

      .header-contact {
        order: 2;
        justify-self: center;
        text-align: center;
        font-size: 0.8rem;
      }

      .brand-icon {
        font-size: 2rem;
      }

      .brand-logo {
        font-size: 1.5rem;
      }

      .brand-tagline {
        font-size: 0.75rem;
      }
    }

    @media screen and (min-width: 600px) {
      .header-nav {
        flex-direction: row;
        gap: var(--spacing-md);
      }
    }

    @media screen and (min-width: 768px) {
      .branded-header {
        padding: var(--spacing-lg) var(--spacing-md);
      }

      .brand-icon {
        font-size: 3rem;
      }

      .brand-logo {
        font-size: 2.2rem;
      }

      .brand-tagline {
        font-size: 0.95rem;
      }

      .header-nav a {
        padding: var(--spacing-sm) var(--spacing-md);
        font-size: 1rem;
      }

      .header-contact {
        font-size: 0.9rem;
      }
    }

    @media screen and (min-width: 1024px) {
      .header-container {
        grid-template-columns: 2fr auto 2fr;
        min-height: 100px;
      }

      .brand-icon {
        font-size: 3.5rem;
      }

      .brand-logo {
        font-size: 2.5rem;
      }

      .brand-tagline {
        font-size: 1rem;
      }

      .header-nav {
        gap: var(--spacing-lg);
      }

      .header-contact {
        font-size: 1rem;
      }
    }

    /* Categories container - Mobile first */
    .categories-container {
      border: 1px solid var(--border-color);
      border-radius: var(--border-radius);
      padding: var(--spacing-sm);
      margin-bottom: var(--spacing-md);
    }

    .categories-header {
      font-weight: bold;
      margin-bottom: var(--spacing-sm);
      padding-bottom: var(--spacing-sm);
      border-bottom: 1px solid #eee;
    }

    .key-groups-container {
      display: grid;
      grid-template-columns: 1fr;
      gap: var(--spacing-sm);
    }

    .key-group {
      border: 1px solid var(--light-border);
      border-radius: var(--border-radius);
      padding: var(--spacing-sm);
      background-color: var(--background-light);
      box-shadow: var(--shadow-light);
    }

    .key-header {
      font-weight: bold;
      margin-bottom: var(--spacing-sm);
      padding-bottom: var(--spacing-xs);
      border-bottom: 1px solid #eee;
      color: var(--text-light);
      position: relative;
    }

    .key-header .group-delete-btn {
      display: none;
      position: absolute;
      right: 30px;
      top: 50%;
      transform: translateY(-50%);
      color: var(--danger-color);
      font-weight: bold;
      cursor: pointer;
      font-size: 1.1rem;
      line-height: 1;
      padding: 2px 4px;
      border-radius: var(--border-radius);
      background: rgba(255, 255, 255, 0.9);
      transition: all 0.2s ease;
    }

    .key-header .group-delete-btn:hover {
      background: var(--danger-color);
      color: white;
    }

    .key-header:hover .group-delete-btn {
      display: block;
    }

    .key-items {
      display: grid;
      grid-template-columns: 1fr;
      gap: var(--spacing-xs);
      max-height: 300px;
      overflow-y: auto;
    }

    .key-items.columns-1 {
      grid-template-columns: 1fr;
    }

    .key-items.columns-2 {
      grid-template-columns: repeat(2, 1fr);
    }

    .key-items.columns-3 {
      grid-template-columns: repeat(3, 1fr);
    }

    .key-items.scrollable {
      max-height: 300px;
      overflow-y: auto;
    }

    /* Actions and buttons */
    .actions {
      margin: var(--spacing-md) 0;
      display: flex;
      flex-direction: column;
      gap: var(--spacing-sm);
    }

    button {
      padding: var(--spacing-sm) var(--spacing-md);
      font-size: 1rem;
      cursor: pointer;
      border: 1px solid var(--primary-color);
      background: var(--primary-color);
      color: white;
      border-radius: var(--border-radius);
      transition: all 0.2s ease;
      font-family: inherit;
    }

    button:hover:not(:disabled) {
      background: #003a6b;
      border-color: #003a6b;
    }

    button:disabled {
      background: var(--text-muted);
      border-color: var(--text-muted);
      cursor: not-allowed;
      opacity: 0.6;
    }

    /* Log area */
    #log {
      margin-top: var(--spacing-md);
      white-space: pre-wrap;
      background: var(--background-lighter);
      padding: var(--spacing-md);
      border-radius: var(--border-radius);
      height: 200px;
      overflow-y: auto;
      font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
      font-size: 0.85rem;
      border: 1px solid var(--light-border);
    }

    .download-link {
      margin: var(--spacing-sm) 0;
      display: block;
      color: var(--primary-color);
      text-decoration: none;
      padding: var(--spacing-xs);
      border-radius: var(--border-radius);
    }

    .download-link:hover {
      text-decoration: underline;
      background: rgba(0, 75, 141, 0.1);
    }
    /* Key Header and Inline Add Styles */
    .key-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      flex-wrap: wrap;
      gap: var(--spacing-xs);
    }

    .key-header .add-new {
      cursor: pointer;
      font-size: 1.2rem;
      color: var(--secondary-color);
      margin-left: var(--spacing-sm);
      transition: color 0.2s ease;
    }

    .key-header .add-new:hover {
      color: #0e8c65;
    }

    /* Category items */
    .category-item {
      position: relative;
      padding: var(--spacing-xs);
      font-size: 0.9rem;
      break-inside: avoid;
      page-break-inside: avoid;
      border-radius: var(--border-radius);
      transition: background-color 0.2s ease;
    }

    .category-item:hover {
      background-color: rgba(0, 75, 141, 0.05);
    }

    .category-item label {
      display: flex;
      align-items: flex-start;
      cursor: pointer;
      width: 100%;
    }

    .category-item input[type="checkbox"] {
      margin-right: var(--spacing-xs);
      margin-top: 3px;
      flex-shrink: 0;
    }

    .category-item .remove-btn {
      display: none;
      position: absolute;
      right: 4px;
      top: 4px;
      color: var(--danger-color);
      font-weight: bold;
      cursor: pointer;
      font-size: 1rem;
      line-height: 1;
      background: rgba(255, 255, 255, 0.9);
      border-radius: 50%;
      width: 20px;
      height: 20px;
      text-align: center;
      transition: all 0.2s ease;
    }

    .category-item:hover .remove-btn {
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .category-item .remove-btn:hover {
      background: var(--danger-color);
      color: white;
    }

    /* New entry styles */
    .category-item.new-entry {
      display: flex;
      align-items: center;
      gap: var(--spacing-sm);
      padding: var(--spacing-xs);
      background-color: rgba(16, 163, 127, 0.1);
    }

    .category-item.new-entry input[type="text"] {
      flex: 1;
      padding: var(--spacing-xs) var(--spacing-sm);
      font-size: 0.9rem;
      border: 1px solid var(--border-color);
      border-radius: var(--border-radius);
      font-family: inherit;
    }

    .category-item.new-entry .cancel-new {
      color: var(--danger-color);
      cursor: pointer;
      font-size: 1.1rem;
      line-height: 1;
      transition: color 0.2s ease;
    }

    .category-item.new-entry .cancel-new:hover {
      color: #b71c1c;
    }

    .category-item.new-entry .ask-chatgpt-link {
      font-size: 0.85rem;
      text-decoration: none;
      color: var(--primary-color);
      white-space: nowrap;
      transition: color 0.2s ease;
    }

    .category-item.new-entry .ask-chatgpt-link:hover {
      text-decoration: underline;
      color: #003a6b;
    }

    /* Header Add Group Styles */
    .categories-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      flex-wrap: wrap;
      gap: var(--spacing-xs);
    }

    .categories-header .add-group {
      cursor: pointer;
      font-size: 1.2rem;
      color: var(--secondary-color);
      margin-left: var(--spacing-sm);
      transition: color 0.2s ease;
    }

    .categories-header .add-group:hover {
      color: #0e8c65;
    }

    .header-new-entry {
      display: flex;
      align-items: center;
      gap: var(--spacing-sm);
      margin-top: var(--spacing-sm);
      flex-wrap: wrap;
    }

    .header-new-entry input[type="text"] {
      flex: 1;
      min-width: 200px;
      padding: var(--spacing-xs) var(--spacing-sm);
      font-size: 0.9rem;
      border: 1px solid var(--border-color);
      border-radius: var(--border-radius);
      font-family: inherit;
    }

    .header-new-entry .cancel-new {
      color: var(--danger-color);
      cursor: pointer;
      font-size: 1.1rem;
      line-height: 1;
      transition: color 0.2s ease;
    }

    .header-new-entry .cancel-new:hover {
      color: #b71c1c;
    }

    .header-new-entry .ask-chatgpt-link {
      font-size: 0.85rem;
      text-decoration: none;
      color: var(--primary-color);
      white-space: nowrap;
      transition: color 0.2s ease;
    }

    .header-new-entry .ask-chatgpt-link:hover {
      text-decoration: underline;
      color: #003a6b;
    }

    /* POPUP OVERLAY STYLES */
    .hidden-overlay {
      display: none;
      position: fixed;
      top: 0;
      left: 0;
      width: 100vw;
      height: 100vh;
      background: rgba(0, 0, 0, 0.6);
      justify-content: center;
      align-items: center;
      z-index: 1000;
      padding: var(--spacing-md);
    }

    .visible-overlay {
      display: flex;
    }

    /* Story Dialog */
    .story-dialog {
      background: #fff;
      border-radius: var(--border-radius-large);
      max-width: 600px;
      width: 100%;
      max-height: 90vh;
      padding: var(--spacing-lg);
      box-shadow: var(--shadow-medium);
      position: relative;
      overflow: hidden;
      display: flex;
      flex-direction: column;
    }

    .story-header h2 {
      margin: 0 0 var(--spacing-md) 0;
      color: var(--primary-color);
      font-size: 1.5rem;
      padding-right: 40px;
    }

    .story-content {
      flex: 1;
      overflow-y: auto;
      font-family: inherit;
      font-size: 0.95rem;
      line-height: 1.6;
    }

    .story-content ul {
      padding-left: var(--spacing-lg);
      margin: 0;
    }

    .story-content li {
      margin-bottom: var(--spacing-sm);
    }

    .signature-block {
      margin-top: var(--spacing-lg);
      padding-top: var(--spacing-md);
      border-top: 1px solid #eee;
      font-weight: 500;
    }

    .signature-block a {
      display: block;
      margin-top: var(--spacing-xs);
      color: var(--primary-color);
      text-decoration: none;
      transition: color 0.2s ease;
    }

    .signature-block a:hover {
      text-decoration: underline;
      color: #003a6b;
    }

    .close-btn {
      position: absolute;
      top: var(--spacing-sm);
      right: var(--spacing-sm);
      background: transparent;
      border: none;
      font-size: 1.5rem;
      width: 30px;
      height: 30px;
      line-height: 30px;
      text-align: center;
      color: var(--text-muted);
      cursor: pointer;
      border-radius: 50%;
      transition: all 0.2s ease;
    }

    .close-btn:hover {
      color: var(--text-color);
      background: rgba(0, 0, 0, 0.1);
    }

    /* INSTRUCTIONS POPUP STYLES */
    .instructions-dialog {
      background: #fff;
      border-radius: var(--border-radius-large);
      max-width: 95vw;
      width: 100%;
      max-width: 1200px;
      height: 85vh;
      max-height: 800px;
      padding: var(--spacing-lg);
      box-shadow: var(--shadow-medium);
      position: relative;
      display: flex;
      flex-direction: column;
    }

    .instructions-header h2 {
      margin: 0 0 var(--spacing-md) 0;
      color: var(--primary-color);
      font-size: 1.5rem;
      padding-right: 40px;
    }

    .instructions-content {
      flex: 1;
      overflow: auto;
      border: 1px solid #ddd;
      border-radius: var(--border-radius);
      background: #fff;
    }

    .markdown-content {
      padding: var(--spacing-md);
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Helvetica, Arial, sans-serif;
      line-height: 1.6;
      color: #24292f;
      max-width: none;
    }

    .markdown-content h1 {
      font-size: 1.75rem;
      font-weight: 600;
      margin: 0 0 var(--spacing-md) 0;
      padding-bottom: var(--spacing-xs);
      border-bottom: 1px solid #d1d9e0;
      color: var(--primary-color);
    }

    .markdown-content h2 {
      font-size: 1.25rem;
      font-weight: 600;
      margin: var(--spacing-xl) 0 var(--spacing-md) 0;
      padding-bottom: var(--spacing-xs);
      border-bottom: 1px solid #d1d9e0;
      color: var(--primary-color);
    }
    .markdown-content h3 {
      font-size: 1.25rem;
      font-weight: 600;
      margin: 1.5rem 0 0.5rem 0;
      color: #004B8D;
    }
    .markdown-content h4 {
      font-size: 1rem;
      font-weight: 600;
      margin: 1rem 0 0.5rem 0;
      color: #004B8D;
    }
    .markdown-content p {
      margin: 0 0 1rem 0;
    }
    .markdown-content ul, .markdown-content ol {
      margin: 0 0 1rem 0;
      padding-left: 2rem;
    }
    .markdown-content li {
      margin: 0.25rem 0;
    }
    .markdown-content code {
      background: #f6f8fa;
      padding: 0.2rem 0.4rem;
      border-radius: 3px;
      font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
      font-size: 0.85rem;
    }
    .markdown-content pre {
      background: #f6f8fa;
      padding: 1rem;
      border-radius: 6px;
      overflow-x: auto;
      margin: 1rem 0;
    }
    .markdown-content pre code {
      background: none;
      padding: 0;
    }
    .markdown-content blockquote {
      border-left: 4px solid #d1d9e0;
      padding: 0 1rem;
      margin: 1rem 0;
      color: #656d76;
    }
    .markdown-content a {
      color: #0969da;
      text-decoration: none;
    }
    .markdown-content a:hover {
      text-decoration: underline;
    }

    /* Internal anchor links styling */
    .markdown-content a.internal-link {
      color: var(--primary-color);
      cursor: pointer;
      transition: color 0.2s ease;
    }

    .markdown-content a.internal-link:hover {
      color: #003a6b;
      text-decoration: underline;
    }

    /* Smooth scrolling for instructions content */
    .instructions-content {
      scroll-behavior: smooth;
    }
    .markdown-content strong {
      font-weight: 600;
    }
    .markdown-content em {
      font-style: italic;
    }
    .markdown-content hr {
      border: none;
      border-top: 1px solid #d1d9e0;
      margin: 2rem 0;
    }
    .loading-message {
      text-align: center;
      padding: 2rem;
      color: #656d76;
      font-style: italic;
    }
    .error-message {
      text-align: center;
      padding: 2rem;
      color: #d1242f;
      background: #fff8f8;
      border: 1px solid #f8d7da;
      border-radius: 4px;
      margin: 1rem;
    }

    /* ZIP code and coordinates styling */
    #coordsDisplay {
      font-family: Consolas, monospace;
      font-weight: 500;
      color: #004B8D;
    }

    .coords-info {
      color: #666;
    }

    .status-message {
      font-size: 0.9rem;
      margin-top: 5px;
      font-style: italic;
    }

    #radiusMiles {
      width: 60px;
    }

    #zipCode {
      width: 70px;
    }

    .chatgpt-link {
      margin-left: 10px;
    }

    #resetCategoriesLink {
      margin-left: 10px;
      color: #d32f2f;
      font-weight: 500;
    }

    #restoreGroupsLink {
      margin-left: 10px;
      color: #1976d2;
      font-weight: 500;
    }

    /* ========================================
       RESPONSIVE MEDIA QUERIES
       ======================================== */

    /* Very small screens - 157px width (smartwatch/mini displays) */
    @media screen and (max-width: 157px) {
      body {
        padding: var(--spacing-xs);
        font-size: 0.75rem;
      }

      .main-nav {
        flex-direction: column;
        gap: var(--spacing-xs);
      }

      .brand-icon {
        font-size: 1.5rem;
      }

      .brand-logo {
        font-size: 1.2rem;
      }

      .brand-tagline {
        font-size: 0.65rem;
      }

      .header-nav a {
        font-size: 0.7rem;
        padding: var(--spacing-xs);
      }

      .header-contact {
        font-size: 0.65rem;
      }

      h1 {
        font-size: 1rem;
        line-height: 1.2;
      }

      .categories-container {
        padding: var(--spacing-xs);
      }

      .key-groups-container {
        grid-template-columns: 1fr;
        gap: var(--spacing-xs);
      }

      .key-items.columns-2,
      .key-items.columns-3 {
        grid-template-columns: 1fr;
      }

      .actions {
        gap: var(--spacing-xs);
      }

      button {
        padding: var(--spacing-xs);
        font-size: 0.8rem;
      }

      #log {
        height: 150px;
        font-size: 0.7rem;
        padding: var(--spacing-xs);
      }

      .story-dialog,
      .instructions-dialog {
        width: 95vw;
        height: 95vh;
        padding: var(--spacing-sm);
      }

      .markdown-content {
        padding: var(--spacing-sm);
        font-size: 0.8rem;
      }
    }

    /* Small mobile screens - 360x740 (Galaxy S8/S9) */
    @media screen and (min-width: 360px) and (max-width: 390px) {
      body {
        padding: var(--spacing-sm);
      }

      .brand-icon {
        font-size: 1.8rem;
      }

      .brand-logo {
        font-size: 1.4rem;
      }

      .brand-tagline {
        font-size: 0.7rem;
      }

      .header-contact {
        font-size: 0.75rem;
      }

      .categories-container {
        overflow: hidden;
      }

      .key-groups-container {
        grid-template-columns: 1fr;
      }

      .key-items.columns-2 {
        grid-template-columns: repeat(2, 1fr);
      }

      .key-items.columns-3 {
        grid-template-columns: repeat(2, 1fr);
      }

      .actions {
        flex-direction: row;
        flex-wrap: wrap;
      }

      .story-dialog {
        width: 95vw;
        max-height: 85vh;
      }

      .instructions-dialog {
        width: 95vw;
        height: 85vh;
      }
    }

    /* iPhone 12/13/14 - 390x844 */
    @media screen and (min-width: 390px) and (max-width: 430px) {
      .key-groups-container {
        grid-template-columns: 1fr;
      }

      .key-items.columns-3 {
        grid-template-columns: repeat(2, 1fr);
      }

      .actions {
        flex-direction: row;
        gap: var(--spacing-sm);
      }

      .story-dialog {
        width: 90vw;
      }

      .instructions-dialog {
        width: 90vw;
        height: 80vh;
      }
    }

    /* iPhone 14 Pro Max - 430x932 */
    @media screen and (min-width: 430px) and (max-width: 480px) {
      .key-groups-container {
        grid-template-columns: 1fr;
      }

      .key-items.columns-3 {
        grid-template-columns: repeat(2, 1fr);
      }
    }

    /* Small tablets and large phones landscape - 480px to 768px */
    @media screen and (min-width: 480px) and (max-width: 767px) {
      body {
        padding: var(--spacing-md);
      }

      .key-groups-container {
        grid-template-columns: repeat(2, 1fr);
      }

      .key-items.columns-3 {
        grid-template-columns: repeat(3, 1fr);
      }

      .actions {
        flex-direction: row;
        justify-content: flex-start;
      }

      .story-dialog {
        width: 85vw;
        max-width: 600px;
      }

      .instructions-dialog {
        width: 85vw;
        max-width: 700px;
      }
    }

    /* Tablets - 768x1024 (iPad) */
    @media screen and (min-width: 768px) and (max-width: 1023px) {
      body {
        padding: var(--spacing-lg);
      }

      .main-nav {
        flex-direction: row;
        justify-content: space-between;
      }

      .key-groups-container {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--spacing-md);
      }

      .actions {
        flex-direction: row;
        justify-content: flex-start;
        gap: var(--spacing-md);
      }

      .story-dialog {
        width: 80vw;
        max-width: 700px;
      }

      .instructions-dialog {
        width: 85vw;
        max-width: 900px;
        height: 85vh;
      }

      #log {
        height: 250px;
      }
    }

    /* Large tablets landscape - 820x1180 */
    @media screen and (min-width: 820px) and (max-width: 1023px) {
      .key-groups-container {
        grid-template-columns: repeat(3, 1fr);
      }

      .instructions-dialog {
        width: 90vw;
        max-width: 1000px;
      }
    }

    /* Desktop and large screens - 1024x1366+ */
    @media screen and (min-width: 1024px) {
      body {
        padding: var(--spacing-xl);
        max-width: 1400px;
        margin: 0 auto;
      }

      .main-nav {
        justify-content: flex-start;
        gap: var(--spacing-lg);
      }

      .key-groups-container {
        grid-template-columns: repeat(3, 1fr);
        gap: var(--spacing-lg);
      }

      .key-items.columns-3 {
        grid-template-columns: repeat(3, 1fr);
      }

      .actions {
        flex-direction: row;
        gap: var(--spacing-lg);
      }

      .story-dialog {
        width: 70vw;
        max-width: 800px;
      }

      .instructions-dialog {
        width: 80vw;
        max-width: 1200px;
        height: 80vh;
      }

      #log {
        height: 300px;
      }
    }

    /* Large desktop screens - 1368px+ */
    @media screen and (min-width: 1368px) {
      .key-groups-container {
        grid-template-columns: repeat(4, 1fr);
      }

      .instructions-dialog {
        width: 75vw;
      }
    }

    /* Landscape orientation adjustments */
    @media screen and (orientation: landscape) and (max-height: 500px) {
      .story-dialog,
      .instructions-dialog {
        height: 90vh;
        max-height: none;
      }

      .story-content,
      .instructions-content {
        max-height: 60vh;
      }

      #log {
        height: 150px;
      }
    }

    /* High DPI displays */
    @media screen and (-webkit-min-device-pixel-ratio: 2),
           screen and (min-resolution: 192dpi) {
      .markdown-content {
        font-size: 0.9rem;
      }

      button {
        padding: calc(var(--spacing-sm) + 1px) calc(var(--spacing-md) + 2px);
      }
    }

    /* Page Header Styles */
    .page-header {
      background: linear-gradient(135deg, var(--background-light) 0%, #f0f8ff 100%);
      border: 1px solid var(--light-border);
      border-radius: var(--border-radius-large);
      padding: var(--spacing-lg) var(--spacing-md);
      margin-bottom: var(--spacing-lg);
      box-shadow: var(--shadow-light);
    }

    .page-header h2 {
      color: var(--primary-color);
      font-size: 1.5rem;
      font-weight: 600;
      margin: 0 0 var(--spacing-md) 0;
      text-align: center;
    }

    .page-header  {
      font-size: 1rem;
      line-height: 1.6;
      color: var(--text-light);
      text-align: center;
      margin: 0;
    }
    .page-description {
      font-size: 1rem;
      line-height: 1.6;
      color: var(--text-light);
      margin: 0;
    }

    .page-header .page-description a {
      color: var(--primary-color);
      text-decoration: none;
      transition: color 0.2s ease;
    }

    .page-header .page-description a:hover {
      color: #003a6b;
      text-decoration: underline;
    }

    .page-controls {
      display: flex;
      flex-direction: column;
      gap: var(--spacing-sm);
      align-items: center;
      margin-top: var(--spacing-md);
      padding-top: var(--spacing-md);
      border-top: 1px solid var(--light-border);
    }

    .page-controls .control-group {
      display: flex;
      flex-wrap: wrap;
      gap: var(--spacing-sm);
      align-items: center;
      justify-content: center;
    }

    .page-controls input[type="number"],
    .page-controls input[type="text"] {
      border: 1px solid var(--border-color);
      border-radius: var(--border-radius);
      padding: var(--spacing-xs) var(--spacing-sm);
      font-family: inherit;
      font-size: 0.9rem;
    }

    .page-controls .coords-info {
      font-size: 0.85rem;
      color: var(--text-muted);
      text-align: center;
      margin-top: var(--spacing-xs);
    }

    .page-controls .action-links {
      display: flex;
      flex-wrap: wrap;
      gap: var(--spacing-sm);
      justify-content: center;
      margin-top: var(--spacing-sm);
    }

    .page-controls .action-links a {
      font-size: 0.9rem;
      color: var(--primary-color);
      text-decoration: none;
      padding: var(--spacing-xs) var(--spacing-sm);
      border-radius: var(--border-radius);
      transition: all 0.2s ease;
      border: 1px solid transparent;
    }

    .page-controls .action-links a:hover {
      background: rgba(0, 75, 141, 0.1);
      border-color: var(--primary-color);
      text-decoration: none;
    }

    .page-controls .action-links a#resetCategoriesLink {
      color: var(--danger-color);
    }

    .page-controls .action-links a#resetCategoriesLink:hover {
      background: rgba(211, 47, 47, 0.1);
      border-color: var(--danger-color);
    }

    .page-controls .action-links a#restoreGroupsLink {
      color: var(--success-color);
    }

    .page-controls .action-links a#restoreGroupsLink:hover {
      background: rgba(25, 118, 210, 0.1);
      border-color: var(--success-color);
    }

    /* Footer Styles */
    footer {
      margin-top: var(--spacing-xl);
      padding: var(--spacing-lg) var(--spacing-sm);
      background: linear-gradient(135deg, var(--background-light) 0%, var(--background-lighter) 100%);
      border-top: 2px solid var(--light-border);
      border-radius: var(--border-radius-large) var(--border-radius-large) 0 0;
      box-shadow: var(--shadow-light);
    }

    .footer-settings {
      margin-bottom: var(--spacing-lg);
      padding-bottom: var(--spacing-lg);
      border-bottom: 1px solid var(--light-border);
    }

    .footer-settings h3 {
      color: var(--primary-color);
      font-size: 1.1rem;
      font-weight: 600;
      margin: 0 0 var(--spacing-md) 0;
      text-align: center;
    }

    .footer-content {
      max-width: 1200px;
      margin: 0 auto;
      display: grid;
      grid-template-columns: 1fr;
      gap: var(--spacing-md);
      text-align: center;
    }

    .footer-section h3 {
      color: var(--primary-color);
      font-size: 1.1rem;
      font-weight: 600;
      margin: 0 0 var(--spacing-sm) 0;
      border-bottom: 1px solid var(--light-border);
      padding-bottom: var(--spacing-xs);
    }

    .footer-section p,
    .footer-section ul {
      margin: 0 0 var(--spacing-sm) 0;
      font-size: 0.9rem;
      line-height: 1.5;
      color: var(--text-light);
    }

    .footer-section ul {
      list-style: none;
      padding: 0;
    }

    .footer-section ul li {
      margin: var(--spacing-xs) 0;
    }

    .footer-section a {
      color: var(--primary-color);
      text-decoration: none;
      transition: color 0.2s ease;
    }

    .footer-section a:hover {
      color: #003a6b;
      text-decoration: underline;
    }

    .footer-bottom {
      margin-top: var(--spacing-md);
      padding-top: var(--spacing-md);
      border-top: 1px solid var(--light-border);
      text-align: center;
      font-size: 0.85rem;
      color: var(--text-muted);
    }

    .footer-bottom p {
      margin: 0;
    }

    /* Results Container responsive styles */
    @media screen and (min-width: 600px) {
      .downloads-grid {
        grid-template-columns: repeat(2, 1fr);
      }

      .results-header {
        flex-direction: row;
      }

      .results-actions {
        flex-direction: row;
        gap: var(--spacing-sm);
      }
    }

    /* Mobile responsive styles for download button */
    @media screen and (max-width: 600px) {
      .download-all-files-btn {
        font-size: 0.85rem;
        padding: var(--spacing-xs);
        margin-right: 0;
        margin-bottom: var(--spacing-xs);
        width: 100%;
        text-align: center;
      }

      .results-actions {
        flex-direction: column;
        gap: var(--spacing-xs);
      }
    }

    @media screen and (min-width: 1024px) {
      .downloads-grid {
        grid-template-columns: repeat(3, 1fr);
      }
    }

    /* Page Header responsive styles */
    @media screen and (min-width: 600px) {
      .page-header h2 {
        font-size: 1.75rem;
      }

      .page-controls {
        flex-direction: row;
        justify-content: center;
        flex-wrap: wrap;
      }

      .page-controls .control-group {
        flex-direction: row;
      }
    }

    @media screen and (min-width: 768px) {
      .page-header {
        padding: var(--spacing-xl) var(--spacing-lg);
      }

      .page-header h2 {
        font-size: 2rem;
      }

      .page-header .page-description {
        font-size: 1.1rem;
      }
    }

    @media screen and (min-width: 1024px) {
      .page-header h2 {
        font-size: 2.25rem;
      }
    }

    /* Footer responsive styles */
    @media screen and (min-width: 600px) {
      .footer-settings {
        text-align: left;
      }

      .footer-settings .settings-section {
        flex-direction: row;
        align-items: center;
        gap: var(--spacing-md);
      }

      .footer-content {
        grid-template-columns: repeat(2, 1fr);
        text-align: left;
      }
    }

    @media screen and (min-width: 768px) {
      footer {
        padding: var(--spacing-xl) var(--spacing-md);
      }

      .footer-content {
        grid-template-columns: repeat(3, 1fr);
        gap: var(--spacing-lg);
      }
    }

    @media screen and (min-width: 1024px) {
      .footer-content {
        grid-template-columns: repeat(4, 1fr);
      }
    }

    /* Print styles */
    @media print {
      .main-nav,
      .actions,
      .hidden-overlay,
      .visible-overlay {
        display: none !important;
      }

      body {
        padding: 0;
        font-size: 12pt;
        line-height: 1.4;
      }

      .categories-container {
        break-inside: avoid;
      }

      .key-group {
        break-inside: avoid;
        margin-bottom: 1rem;
      }

      #log {
        height: auto;
        max-height: 200px;
        font-size: 10pt;
      }
    }
  </style>
  <!-- Add JSZip library in the head section -->
  <script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js"></script>
</head>
<body>
  <!-- STORY OF PAGE POPUP -->
  <div id="pageStoryOverlay" class="hidden-overlay">
    <div id="pageStoryDialog" class="story-dialog" tabindex="-1">
      <button id="closePageStory" class="close-btn" aria-label="Close">&times;</button>
      <header class="story-header">
        <h2>The Story of This Page</h2>
      </header>
      <div class="story-content">
        <ul>
          <li>This page was originally described as a <a href="https://chatgpt.com/share/683fae9c-f324-8013-b4ba-b9b978770b26" target="_blank">ChatGPT prompt</a>.</li>
          <li>ChatGPT generated a suitable Python program to accomplish the task, but to avoid Python dependencies, the reply was refactored as an HTML page.  This allows anyone to use the page without needing to install Python.</li>
          <li>Again, to avoid Google Dependencies, the mapping engine was switched from Google Maps to OpenStreetMap.  This allows for anyone to use the page without needing a Google API key.</li>
          <li>The resulting code was copy-and-pasted into VS Code and opened with <a href="https://www.augmentcode.com/" target="_blank">Augment</a>.</li>
          <li>Within VS Code + Augment, this project was massaged and refactored multiple times.  Ultimately, the use of the Augment AI Agent is the best way to '<i>vibe code</i>.'</li>
          <li>The code was also pushed to GitHub: <a href="https://github.com/mytech-today-now/business_search" target="_blank">mytech-today-now/business_search</a>.</li>
          <li>A separate ChatGPT prompt produced 35 business types that benefit from MSP services in Chicagoland; the reply was reformatted as a comma-separated list and re-fed into ChatGPT/Augment.</li>
          <li>On June 1, 2025, the code was refactored 29 times using Augment (50 total prompts to generate and refine code). Additional prompts refined and improved the final version.</li>
          <li>Finally, the entire codebase was fed back into ChatGPT for further prompt generation, then re-applied in Augment. This iterative AI-driven workflow has been an extremely effective way to "vibe code.  Currently, June 5, 2025, Augment does a better job of 'vibe coding' than ChatGPT with Codex"</li>
          <li>By comparison, 15 years ago I wrote a similar program in C# over the course of four weeks. This AI version took 1.5 days with almost zero manual effort, and is much more professional, comphrensive, interactive, and responsive.</li>
        </ul>
        <div class="signature-block">
          Kyle Rode
          <a href="https://mytech.today" target="_blank">myTech.Today</a>
          <a href="tel:***********" target="_blank">(*************</a>
          <a href="mailto:<EMAIL>" target="_blank"><EMAIL></a>
        </div>
      </div>
    </div>
  </div>

  <!-- INSTRUCTIONS POPUP -->
  <div id="instructionsOverlay" class="hidden-overlay">
    <div id="instructionsDialog" class="instructions-dialog" tabindex="-1">
      <button id="closeInstructions" class="close-btn" aria-label="Close">&times;</button>
      <header class="instructions-header">
        <h2>Instructions & Documentation</h2>
      </header>
      <div class="instructions-content">
        <div id="instructionsContent" class="markdown-content">
          <div class="loading-message">Loading documentation...</div>
        </div>
      </div>
    </div>
  </div>

  <!-- TEMPORARY TEST RESULTS DIV -->
  <div id="testResults" style="position: fixed; top: 10px; right: 10px; background: #fff; border: 2px solid #004B8D; padding: 10px; border-radius: 5px; max-width: 300px; z-index: 10000; font-size: 12px; display: none;">
    <h4 style="margin: 0 0 10px 0; color: #004B8D;">Markdown Test Results</h4>
    <div id="testContent"></div>
  </div>

  <header class="branded-header">
    <div class="header-container">
      <nav class="header-nav">
        <a href="#" id="openInstructions">📖 Instructions</a>
        <a href="#" id="openPageStory">📜 Story</a>
      </nav>

      <div class="header-brand">
        <div class="brand-icon">🏢</div>
        <h1 class="brand-logo"><a href="https://mytech.today" target="_blank" rel="noopener noreferrer">myTech.Today</a></h1>
        <p class="brand-tagline">Professional MSP Services • Barrington, IL • (*************</p>
      </div>

      <div class="header-contact">
        <a href="mailto:<EMAIL>?subject=Export%20Businesses" target="_blank"><EMAIL></a>
      </div>
    </div>
  </header>

  <!-- Page Header Section -->
  <section class="page-header">
    <h1>🚀 FREE Business Lead Generator: Export 1000s of Local Businesses in Minutes! No API Required | myTech.Today</h1>
    <p class="page-subtitle">Generate unlimited business leads instantly! Export thousands of local businesses with contact info, addresses & phone numbers. Free tool uses OpenStreetMap - no Google API needed. Perfect for MSPs, marketers & sales teams in Chicago, Illinois.</p>
    <h3>Export Businesses (<span id="radiusDisplay">30</span> mi radius around <span id="zipDisplay">60010</span>) as CSV</h3>
    <p class="page-description">
      This page uses the Overpass API (<a href="https://www.openstreetmap.org/about" target="_blank" rel="noopener noreferrer">OpenStreetMap</a>) to fetch business data. Select categories below and click "Run Export" to generate CSVs. No API key is required for basic functionality.
    </p>

    <div class="page-controls">
      <div class="control-group">
        <label for="radiusMiles">Search radius:</label>
        <input type="number" id="radiusMiles" value="30" min="1" max="100" title="Search radius in miles"> mi
        (<span id="radiusMeters">48280</span>m)
      </div>

      <div class="control-group">
        <label for="zipCode">around ZIP:</label>
        <input type="text" id="zipCode" value="60010" pattern="[0-9]{5}" maxlength="5" title="Enter a 5-digit ZIP code and press Enter or click away to update coordinates">
      </div>

      <div class="coords-info">
        Current coordinates: <span id="coordsDisplay">42.1543, -88.1362</span>
      </div>

      <div class="action-links">
        <a href="#" id="askChatGPTLink" target="_blank" rel="noopener noreferrer">Ask ChatGPT for OSM tags</a>
        <a href="#" id="resetCategoriesLink">Reset Categories</a>
        <a href="#" id="restoreGroupsLink">Restore Hidden Groups</a>
      </div>
    </div>
  </section>

  <main>

  <div class="categories-container">
    <div class="categories-header">
      <label><input type="checkbox" id="selectAllCategories"> Select/Unselect All Categories</label>
      <span class="add-group" title="Add New Group">+</span>
    </div>
    <div id="headerNewEntryContainer"></div>
    <div class="category-items" id="allCategories"></div>
  </div>

  <div class="actions">
    <button id="runBtn">Run Export</button>
    <button id="downloadAllBtn" disabled>Download All as ZIP</button>
  </div>

  <div id="log"></div>
  <div id="downloads"></div>

  <script>
    (function () {
      // ======================================================
      // User Categories Storage Helpers
      // ======================================================
      function loadUserCategories() {
        try {
          const stored = localStorage.getItem("userCategories");
          if (!stored) return {};
          return JSON.parse(stored) || {};
        } catch (err) {
          console.error("Error loading user categories:", err);
          return {};
        }
      }
      
      function saveUserCategories(userCategories) {
        try {
          localStorage.setItem("userCategories", JSON.stringify(userCategories));
          return true;
        } catch (err) {
          console.error("Error saving user categories:", err);
          return false;
        }
      }

      // ======================================================
      // User Groups Storage Helpers
      // ======================================================
      function loadUserGroups() {
        try {
          const stored = localStorage.getItem("userGroups");
          if (!stored) return [];
          return JSON.parse(stored) || [];
        } catch (err) {
          console.error("Error loading user groups:", err);
          return [];
        }
      }

      function saveUserGroups(userGroups) {
        try {
          localStorage.setItem("userGroups", JSON.stringify(userGroups));
          return true;
        } catch (err) {
          console.error("Error saving user groups:", err);
          return false;
        }
      }
      
      // ======================================================
      // Configuration
      // ======================================================
      // Default coordinates for ZIP 60010 (Barrington, IL) - make global
      window.LAT = 42.1543;
      window.LNG = -88.1362;
      window.ZIP_CODE = "60010";

      // Default radius: 30 miles - make global
      const MILES_TO_METERS = 1609.34;
      let RADIUS_MILES = 30;
      window.RADIUS = Math.round(RADIUS_MILES * MILES_TO_METERS);

      // DOM elements for radius and ZIP
      const radiusMilesInput = document.getElementById("radiusMiles");
      const radiusMetersSpan = document.getElementById("radiusMeters");
      const radiusDisplay = document.getElementById("radiusDisplay");
      const zipCodeInput = document.getElementById("zipCode");
      const zipDisplay = document.getElementById("zipDisplay");
      const coordsDisplay = document.getElementById("coordsDisplay");
      const statusMessage = document.createElement("div");
      statusMessage.className = "status-message";
      zipCodeInput.parentNode.appendChild(statusMessage);

      // Update radius in meters when miles input changes
      radiusMilesInput.addEventListener("input", function() {
        RADIUS_MILES = parseFloat(this.value) || 30;
        window.RADIUS = Math.round(RADIUS_MILES * MILES_TO_METERS);
        radiusMetersSpan.textContent = window.RADIUS;
        radiusDisplay.textContent = RADIUS_MILES;
      });

      // Geocode ZIP code using Census Geocoding API - make it globally accessible
      window.geocodeZipCode = async function geocodeZipCode(zipCode) {
        statusMessage.textContent = `Looking up coordinates for ${zipCode}...`;
        statusMessage.style.color = "#666";

        try {
          // Try using Census Geocoding API with no-cors mode
          try {
            const response = await fetch(`https://geocoding.geo.census.gov/geocoder/locations/address?benchmark=2020&format=json&zip=${zipCode}`, {
              mode: 'no-cors',
              headers: {
                'Accept': 'application/json'
              }
            });

            // Note: With no-cors, we can't actually read the response content
            // So we'll immediately fall back to our local database
            throw new Error("Using no-cors mode, falling back to local database");

          } catch (corsError) {
            console.warn("CORS issue with Census API, using fallback method");
            // Continue to fallback method
          }
          
          // Fallback: Use a hardcoded mapping of common ZIP codes
          const zipCoordinates = {
            // North Suburbs
            "60002": { lat: 42.4639, lng: -87.9957 }, // Antioch, IL
            "60004": { lat: 42.0828, lng: -87.9803 }, // Arlington Heights, IL
            "60005": { lat: 42.0654, lng: -87.9806 }, // Arlington Heights, IL
            "60007": { lat: 42.0372, lng: -87.9925 }, // Elk Grove Village, IL
            "60008": { lat: 42.0503, lng: -88.0198 }, // Rolling Meadows, IL
            "60010": { lat: 42.1543, lng: -88.1362 }, // Barrington, IL
            "60012": { lat: 42.2297, lng: -88.3031 }, // Crystal Lake, IL
            "60013": { lat: 42.2389, lng: -88.3173 }, // Cary, IL
            "60014": { lat: 42.2411, lng: -88.3162 }, // Crystal Lake, IL
            "60015": { lat: 42.1711, lng: -87.7890 }, // Deerfield, IL
            "60016": { lat: 42.0411, lng: -87.8878 }, // Des Plaines, IL
            "60018": { lat: 42.0064, lng: -87.9339 }, // Des Plaines, IL
            "60020": { lat: 42.2364, lng: -88.1962 }, // Fox Lake, IL
            "60021": { lat: 42.1917, lng: -88.2356 }, // Fox River Grove, IL
            "60022": { lat: 42.1464, lng: -87.7859 }, // Glencoe, IL
            "60025": { lat: 42.0697, lng: -87.8270 }, // Glenview, IL
            "60026": { lat: 42.0697, lng: -87.8270 }, // Glenview, IL
            "60029": { lat: 42.0411, lng: -87.8878 }, // Golf, IL
            "60030": { lat: 42.3828, lng: -87.9581 }, // Grayslake, IL
            "60031": { lat: 42.3756, lng: -87.9339 }, // Gurnee, IL
            "60035": { lat: 42.1856, lng: -87.7996 }, // Highland Park, IL
            "60040": { lat: 42.2078, lng: -87.8320 }, // Highwood, IL
            "60041": { lat: 42.4142, lng: -88.0931 }, // Ingleside, IL
            "60042": { lat: 42.4500, lng: -88.0964 }, // Island Lake, IL
            "60043": { lat: 42.1439, lng: -87.7645 }, // Kenilworth, IL
            "60044": { lat: 42.2625, lng: -87.8414 }, // Lake Bluff, IL
            "60045": { lat: 42.2336, lng: -87.8481 }, // Lake Forest, IL
            "60046": { lat: 42.4139, lng: -87.8831 }, // Lake Villa, IL
            "60047": { lat: 42.1950, lng: -88.0797 }, // Lake Zurich, IL
            "60048": { lat: 42.2836, lng: -87.9400 }, // Libertyville, IL
            "60050": { lat: 42.3331, lng: -88.2728 }, // McHenry, IL
            "60051": { lat: 42.3500, lng: -88.2667 }, // McHenry, IL
            "60053": { lat: 42.0664, lng: -87.7479 }, // Morton Grove, IL
            "60056": { lat: 42.0806, lng: -87.9367 }, // Mount Prospect, IL
            "60060": { lat: 42.3142, lng: -87.9464 }, // Mundelein, IL
            "60061": { lat: 42.2178, lng: -87.9125 }, // Vernon Hills, IL
            "60062": { lat: 42.1467, lng: -87.7925 }, // Northbrook, IL
            "60064": { lat: 42.3000, lng: -87.8333 }, // North Chicago, IL
            "60067": { lat: 42.1103, lng: -88.0539 }, // Palatine, IL
            "60068": { lat: 42.0111, lng: -87.8406 }, // Park Ridge, IL
            "60069": { lat: 42.1825, lng: -88.0017 }, // Lincolnshire, IL
            "60070": { lat: 42.1489, lng: -88.0831 }, // Prospect Heights, IL
            "60073": { lat: 42.4500, lng: -88.0000 }, // Round Lake, IL
            "60074": { lat: 42.0483, lng: -88.0817 }, // Palatine, IL
            "60076": { lat: 42.0397, lng: -87.7337 }, // Skokie, IL
            "60077": { lat: 42.0230, lng: -87.7581 }, // Skokie, IL
            "60081": { lat: 42.4167, lng: -88.1333 }, // Spring Grove, IL
            "60082": { lat: 42.1272, lng: -87.8625 }, // Techny, IL
            "60083": { lat: 42.4500, lng: -87.9833 }, // Wadsworth, IL
            "60084": { lat: 42.3333, lng: -88.0000 }, // Wauconda, IL
            "60085": { lat: 42.3639, lng: -87.8447 }, // Waukegan, IL
            "60087": { lat: 42.3639, lng: -87.8447 }, // Waukegan, IL
            "60089": { lat: 42.1492, lng: -87.9264 }, // Buffalo Grove, IL
            "60090": { lat: 42.1417, lng: -87.9278 }, // Wheeling, IL
            "60091": { lat: 42.0780, lng: -87.7106 }, // Wilmette, IL
            "60093": { lat: 42.1083, lng: -87.7340 }, // Winnetka, IL
            "60096": { lat: 42.1967, lng: -87.9472 }, // Wauconda, IL
            "60099": { lat: 42.4464, lng: -87.8031 }, // Zion, IL

            // Chicago City Proper
            "60601": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (The Loop)
            "60602": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (The Loop)
            "60603": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (The Loop)
            "60604": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (The Loop)
            "60605": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (The Loop)
            "60606": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (The Loop)
            "60607": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (The Loop)
            "60608": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (Bridgeport/Chinatown)
            "60609": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (Bridgeport)
            "60610": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (Near North Side)
            "60611": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (Magnificent Mile/Gold Coast)
            "60612": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (Near West Side)
            "60613": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (Lakeview)
            "60614": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (Lincoln Park)
            "60615": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (Kenwood/Hyde Park)
            "60616": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (Near South Side)
            "60617": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (South Shore)
            "60618": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (North Center/Irving Park)
            "60619": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (Chatham)
            "60620": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (Greater Grand Crossing)
            "60621": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (Fuller Park)
            "60622": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (Wicker Park/Bucktown)
            "60623": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (Little Village)
            "60624": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (North Lawndale)
            "60625": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (Albany Park)
            "60626": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (Rogers Park)
            "60628": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (Roseland)
            "60629": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (Gage Park)
            "60630": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (Jefferson Park)
            "60631": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (O'Hare)
            "60632": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (McKinley Park)
            "60633": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (Hegewisch)
            "60634": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (Belmont Cragin)
            "60636": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (New City)
            "60637": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (Woodlawn)
            "60638": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (Ashburn)
            "60639": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (Hermosa)
            "60640": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (Uptown)
            "60641": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (Portage Park)
            "60642": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (Near West Side)
            "60643": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (Morgan Park)
            "60644": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (Austin)
            "60645": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (West Ridge)
            "60646": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (Norwood Park)
            "60647": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (Logan Square)
            "60649": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (South Shore)
            "60651": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (West Garfield Park)
            "60652": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (West Elsdon)
            "60653": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (Oakland)
            "60654": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (River North)
            "60655": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (Mount Greenwood)
            "60656": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (O'Hare)
            "60657": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (Lakeview)
            "60659": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (West Ridge)
            "60660": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (Edgewater)
            "60661": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (West Loop)
            "60664": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (Near North Side)
            "60666": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (O'Hare Airport)
            "60668": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (Near West Side)
            "60669": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (Near West Side)
            "60670": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (The Loop)
            "60673": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (The Loop)
            "60674": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (The Loop)
            "60675": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (The Loop)
            "60677": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (The Loop)
            "60678": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (The Loop)
            "60680": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (The Loop)
            "60681": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (The Loop)
            "60682": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (The Loop)
            "60684": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (The Loop)
            "60685": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (The Loop)
            "60686": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (The Loop)
            "60687": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (The Loop)
            "60688": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (The Loop)
            "60689": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (The Loop)
            "60690": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (The Loop)
            "60691": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (The Loop)
            "60693": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (The Loop)
            "60694": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (The Loop)
            "60695": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (The Loop)
            "60696": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (The Loop)
            "60697": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (The Loop)
            "60699": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (The Loop)

            // Additional Chicago South Side and Neighborhoods
            "60827": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (Calumet Heights)
            "60628": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (Roseland/Pullman)
            "60617": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (South Shore/Rainbow Beach)
            "60649": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (South Shore)
            "60619": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (Chatham/Avalon Park)
            "60620": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (Greater Grand Crossing)
            "60637": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (Woodlawn/University of Chicago)
            "60615": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (Kenwood/Hyde Park)
            "60653": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (Oakland/Douglas)
            "60621": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (Fuller Park/Armour Square)
            "60609": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (Bridgeport/McKinley Park)
            "60608": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (Bridgeport/Chinatown)
            "60616": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (Near South Side/South Loop)
            "60605": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (South Loop/Museum Campus)

            // West Suburbs
            "60101": { lat: 41.9372, lng: -88.0942 }, // Addison, IL
            "60102": { lat: 42.1656, lng: -88.2831 }, // Algonquin, IL
            "60103": { lat: 42.0500, lng: -88.2833 }, // Bartlett, IL
            "60106": { lat: 41.9597, lng: -88.0100 }, // Bensenville, IL
            "60107": { lat: 42.0372, lng: -88.2806 }, // Streamwood, IL
            "60108": { lat: 41.9372, lng: -88.1342 }, // Bloomingdale, IL
            "60109": { lat: 42.0667, lng: -88.8167 }, // Burlington, IL
            "60110": { lat: 42.0978, lng: -88.2789 }, // Carpentersville, IL
            "60115": { lat: 41.9294, lng: -88.7500 }, // DeKalb, IL
            "60118": { lat: 42.0950, lng: -88.2833 }, // East Dundee, IL
            "60119": { lat: 41.8900, lng: -88.4700 }, // Elburn, IL
            "60120": { lat: 42.0372, lng: -88.2806 }, // Elgin, IL
            "60123": { lat: 42.0372, lng: -88.2806 }, // Elgin, IL
            "60124": { lat: 42.0939, lng: -88.2892 }, // Elgin, IL
            "60126": { lat: 41.8994, lng: -87.9403 }, // Elmhurst, IL
            "60133": { lat: 41.9950, lng: -88.1342 }, // Hanover Park, IL
            "60134": { lat: 41.8875, lng: -88.3053 }, // Geneva, IL
            "60136": { lat: 42.1000, lng: -88.3333 }, // Gilberts, IL
            "60139": { lat: 41.9114, lng: -88.0789 }, // Glendale Heights, IL
            "60140": { lat: 42.0833, lng: -88.5333 }, // Hampshire, IL
            "60142": { lat: 42.1667, lng: -88.4500 }, // Huntley, IL
            "60143": { lat: 41.9564, lng: -88.0853 }, // Itasca, IL
            "60152": { lat: 42.2333, lng: -88.4000 }, // Marengo, IL
            "60153": { lat: 41.8764, lng: -87.8631 }, // Maywood, IL
            "60154": { lat: 41.8828, lng: -87.9589 }, // Westchester, IL
            "60156": { lat: 42.2500, lng: -88.3833 }, // Lake in the Hills, IL
            "60157": { lat: 42.0372, lng: -88.1342 }, // Medinah, IL
            "60172": { lat: 42.0089, lng: -88.0797 }, // Roselle, IL
            "60173": { lat: 42.0456, lng: -88.1152 }, // Schaumburg, IL
            "60174": { lat: 41.9142, lng: -88.3087 }, // St. Charles, IL
            "60175": { lat: 41.9142, lng: -88.3087 }, // St. Charles, IL
            "60177": { lat: 42.0333, lng: -88.3333 }, // South Elgin, IL
            "60178": { lat: 42.0500, lng: -88.7167 }, // Sycamore, IL
            "60181": { lat: 41.8700, lng: -87.9650 }, // Villa Park, IL
            "60184": { lat: 41.8517, lng: -88.2075 }, // Wayne, IL
            "60185": { lat: 41.8528, lng: -88.2042 }, // West Chicago, IL
            "60187": { lat: 41.8656, lng: -88.1069 }, // Wheaton, IL
            "60188": { lat: 41.9322, lng: -88.1536 }, // Carol Stream, IL
            "60189": { lat: 41.7950, lng: -88.0700 }, // Wheaton, IL
            "60190": { lat: 41.8244, lng: -88.1561 }, // Winfield, IL
            "60191": { lat: 41.9578, lng: -87.9981 }, // Wood Dale, IL
            "60192": { lat: 42.0303, lng: -88.2073 }, // Hoffman Estates, IL
            "60193": { lat: 42.0303, lng: -88.2073 }, // Schaumburg, IL
            "60194": { lat: 42.0456, lng: -88.1152 }, // Schaumburg, IL
            "60195": { lat: 42.0456, lng: -88.1152 }, // Schaumburg, IL
            
            // Evanston/Skokie
            "60201": { lat: 42.0464, lng: -87.6931 }, // Evanston, IL
            "60202": { lat: 42.0464, lng: -87.6931 }, // Evanston, IL
            "60203": { lat: 42.0464, lng: -87.6931 }, // Evanston, IL
            
            // Oak Park/River Forest
            "60301": { lat: 41.8856, lng: -87.7845 }, // Oak Park, IL
            "60302": { lat: 41.8856, lng: -87.7845 }, // Oak Park, IL
            "60304": { lat: 41.8856, lng: -87.7845 }, // Oak Park, IL
            "60305": { lat: 41.8953, lng: -87.8339 }, // River Forest, IL
            "60521": { lat: 41.7992, lng: -87.9403 }, // Hinsdale, IL
            "60523": { lat: 41.8200, lng: -87.9650 }, // Oak Brook, IL
            "60525": { lat: 41.8056, lng: -87.8631 }, // La Grange, IL
            "60526": { lat: 41.8200, lng: -87.8900 }, // La Grange Park, IL
            "60532": { lat: 41.8089, lng: -88.0756 }, // Lisle, IL
            "60540": { lat: 41.7508, lng: -88.1535 }, // Naperville, IL
            "60563": { lat: 41.7508, lng: -88.1535 }, // Naperville, IL
            "60564": { lat: 41.7508, lng: -88.1535 }, // Naperville, IL
            "60565": { lat: 41.7508, lng: -88.1535 },  // Naperville, IL
            
            // South Bay Los Angeles
            "90245": { lat: 33.9164, lng: -118.4016 }, // El Segundo, CA
            "90254": { lat: 33.8598, lng: -118.3965 }, // Hermosa Beach, CA
            "90266": { lat: 33.8847, lng: -118.4109 }, // Manhattan Beach, CA
            "90277": { lat: 33.8236, lng: -118.3887 }, // Redondo Beach, CA
            "90278": { lat: 33.8729, lng: -118.3765 }, // Redondo Beach, CA
            "90501": { lat: 33.8358, lng: -118.3406 }, // Torrance, CA
            "90503": { lat: 33.8358, lng: -118.3406 }, // Torrance, CA
            "90505": { lat: 33.8058, lng: -118.3312 }, // Torrance, CA
            
            // Palos Verdes Peninsula
            "90274": { lat: 33.7866, lng: -118.3896 }, // Palos Verdes Estates, CA
            "90275": { lat: 33.7444, lng: -118.3870 }, // Rancho Palos Verdes, CA
            
            // San Pedro
            "90731": { lat: 33.7361, lng: -118.2922 }, // San Pedro, CA
            "90732": { lat: 33.7483, lng: -118.3120 }, // San Pedro, CA
            
            // Long Beach
            "90802": { lat: 33.7701, lng: -118.1937 }, // Long Beach, CA
            "90803": { lat: 33.7605, lng: -118.1306 }, // Long Beach, CA
            "90804": { lat: 33.7900, lng: -118.1581 }, // Long Beach, CA
            "90805": { lat: 33.8633, lng: -118.1801 }, // Long Beach, CA
            "90806": { lat: 33.8019, lng: -118.1873 }, // Long Beach, CA
            "90807": { lat: 33.8303, lng: -118.1801 }, // Long Beach, CA
            "90808": { lat: 33.8303, lng: -118.1143 }, // Long Beach, CA
            "90810": { lat: 33.8108, lng: -118.2250 }, // Long Beach, CA
            "90813": { lat: 33.7866, lng: -118.1873 }, // Long Beach, CA
            "90814": { lat: 33.7701, lng: -118.1471 }, // Long Beach, CA
            "90815": { lat: 33.7941, lng: -118.1306 }, // Long Beach, CA
            
            // Venice Beach
            "90291": { lat: 33.9850, lng: -118.4695 }, // Venice, CA
            
            // Culver City
            "90230": { lat: 34.0211, lng: -118.3965 }, // Culver City, CA
            "90232": { lat: 34.0211, lng: -118.3965 }, // Culver City, CA
            
            // Hawthorne
            "90250": { lat: 33.9164, lng: -118.3525 }, // Hawthorne, CA
            
            // Ames, IA
            "50010": { lat: 42.0308, lng: -93.6319 }, // Ames, IA
            "50011": { lat: 42.0308, lng: -93.6319 }, // Ames, IA (Iowa State University)
            "50014": { lat: 42.0308, lng: -93.6319 }, // Ames, IA
            
            // New York City
            "10001": { lat: 40.7503, lng: -73.9972 }, // Manhattan, NY
            "10002": { lat: 40.7168, lng: -73.9861 }, // Manhattan, NY
            "10003": { lat: 40.7318, lng: -73.9888 }, // Manhattan, NY
            "10007": { lat: 40.7133, lng: -74.0070 }, // Manhattan, NY
            "10011": { lat: 40.7399, lng: -74.0021 }, // Manhattan, NY
            "10016": { lat: 40.7459, lng: -73.9776 }, // Manhattan, NY
            "10017": { lat: 40.7520, lng: -73.9736 }, // Manhattan, NY
            "10018": { lat: 40.7551, lng: -73.9932 }, // Manhattan, NY
            "10019": { lat: 40.7662, lng: -73.9862 }, // Manhattan, NY
            "10021": { lat: 40.7690, lng: -73.9550 }, // Manhattan, NY
            "10022": { lat: 40.7583, lng: -73.9685 }, // Manhattan, NY
            "10023": { lat: 40.7769, lng: -73.9822 }, // Manhattan, NY
            "10024": { lat: 40.7892, lng: -73.9745 }, // Manhattan, NY
            "10025": { lat: 40.7989, lng: -73.9669 }, // Manhattan, NY
            "10028": { lat: 40.7764, lng: -73.9546 }, // Manhattan, NY
            "10036": { lat: 40.7602, lng: -73.9896 }, // Manhattan, NY
            "10128": { lat: 40.7808, lng: -73.9497 }, // Manhattan, NY
            "11201": { lat: 40.6958, lng: -73.9897 }, // Brooklyn, NY
            "11215": { lat: 40.6710, lng: -73.9860 }, // Brooklyn, NY
            "11217": { lat: 40.6829, lng: -73.9790 }, // Brooklyn, NY
            "11220": { lat: 40.6340, lng: -74.0110 }, // Brooklyn, NY
            "11222": { lat: 40.7277, lng: -73.9443 }, // Brooklyn, NY
            "11231": { lat: 40.6795, lng: -74.0029 }, // Brooklyn, NY
            "11238": { lat: 40.6795, lng: -73.9643 }, // Brooklyn, NY
            "11211": { lat: 40.7121, lng: -73.9538 }, // Brooklyn, NY (Williamsburg)
            "11249": { lat: 40.7121, lng: -73.9538 }, // Brooklyn, NY (Williamsburg)
            "11101": { lat: 40.7464, lng: -73.9394 }, // Queens, NY (Long Island City)
            "11106": { lat: 40.7628, lng: -73.9311 }, // Queens, NY (Astoria)
            "11103": { lat: 40.7628, lng: -73.9125 }, // Queens, NY (Astoria)
            "10451": { lat: 40.8200, lng: -73.9261 }, // Bronx, NY
            "10452": { lat: 40.8367, lng: -73.9172 }, // Bronx, NY
            "10453": { lat: 40.8521, lng: -73.9119 }, // Bronx, NY
            "10301": { lat: 40.6368, lng: -74.1181 }, // Staten Island, NY
            "10304": { lat: 40.6097, lng: -74.0859 }, // Staten Island, NY
            "10305": { lat: 40.5972, lng: -74.0759 }, // Staten Island, NY
            
            // Omaha, NE
            "68102": { lat: 41.2627, lng: -95.9350 }, // Omaha, NE (Downtown)
            "68104": { lat: 41.2917, lng: -96.0066 }, // Omaha, NE
            "68105": { lat: 41.2431, lng: -95.9577 }, // Omaha, NE
            "68106": { lat: 41.2431, lng: -96.0066 }, // Omaha, NE
            "68107": { lat: 41.2139, lng: -95.9577 }, // Omaha, NE
            "68108": { lat: 41.2335, lng: -95.9350 }, // Omaha, NE
            "68110": { lat: 41.2917, lng: -95.9350 }, // Omaha, NE
            "68111": { lat: 41.2917, lng: -95.9577 }, // Omaha, NE
            "68112": { lat: 41.3403, lng: -95.9577 }, // Omaha, NE
            "68114": { lat: 41.2627, lng: -96.0554 }, // Omaha, NE
            "68116": { lat: 41.2917, lng: -96.1531 }, // Omaha, NE
            "68117": { lat: 41.2139, lng: -96.0066 }, // Omaha, NE
            "68118": { lat: 41.2627, lng: -96.1531 }, // Omaha, NE
            "68130": { lat: 41.2335, lng: -96.1531 }, // Omaha, NE
            "68131": { lat: 41.2723, lng: -95.9577 }, // Omaha, NE
            "68132": { lat: 41.2723, lng: -96.0066 }, // Omaha, NE
            "68134": { lat: 41.2917, lng: -96.0554 }, // Omaha, NE
            "68144": { lat: 41.2139, lng: -96.1043 }, // Omaha, NE
            "68154": { lat: 41.2335, lng: -96.1043 }, // Omaha, NE
            
            // Wilmington, DE
            "19801": { lat: 39.7459, lng: -75.5466 }, // Wilmington, DE (Downtown)
            "19802": { lat: 39.7598, lng: -75.5276 }, // Wilmington, DE
            "19803": { lat: 39.7929, lng: -75.5466 }, // Wilmington, DE
            "19804": { lat: 39.7321, lng: -75.5847 }, // Wilmington, DE
            "19805": { lat: 39.7321, lng: -75.5656 }, // Wilmington, DE
            "19806": { lat: 39.7598, lng: -75.5656 }, // Wilmington, DE
            "19807": { lat: 39.7790, lng: -75.5847 }, // Wilmington, DE
            "19808": { lat: 39.7321, lng: -75.6228 }, // Wilmington, DE
            "19809": { lat: 39.7598, lng: -75.4895 }, // Wilmington, DE
            "19810": { lat: 39.8260, lng: -75.5466 }, // Wilmington, DE
            
            // Winston-Salem, NC
            "27101": { lat: 36.0999, lng: -80.2442 }, // Winston-Salem, NC (Downtown)
            "27103": { lat: 36.0638, lng: -80.3218 }, // Winston-Salem, NC
            "27104": { lat: 36.0999, lng: -80.3218 }, // Winston-Salem, NC
            "27105": { lat: 36.1360, lng: -80.2442 }, // Winston-Salem, NC
            "27106": { lat: 36.1360, lng: -80.3218 }, // Winston-Salem, NC
            "27107": { lat: 36.0277, lng: -80.2442 }, // Winston-Salem, NC
            "27127": { lat: 36.0277, lng: -80.3218 }, // Winston-Salem, NC
            
            // San Fernando Valley & Thousand Oaks
            "91301": { lat: 34.1517, lng: -118.7798 }, // Agoura Hills, CA
            "91302": { lat: 34.1478, lng: -118.7126 }, // Calabasas, CA
            "91303": { lat: 34.2011, lng: -118.6031 }, // Canoga Park, CA
            "91304": { lat: 34.2011, lng: -118.6031 }, // Canoga Park, CA
            "91306": { lat: 34.2011, lng: -118.5257 }, // Winnetka, CA
            "91307": { lat: 34.1883, lng: -118.6417 }, // West Hills, CA
            "91311": { lat: 34.2501, lng: -118.6417 }, // Chatsworth, CA
            "91316": { lat: 34.1517, lng: -118.5031 }, // Encino, CA
            "91324": { lat: 34.2351, lng: -118.5386 }, // Northridge, CA
            "91325": { lat: 34.2351, lng: -118.5386 }, // Northridge, CA
            "91330": { lat: 34.2410, lng: -118.5300 }, // Northridge, CA (CSUN)
            "91335": { lat: 34.2011, lng: -118.5386 }, // Reseda, CA
            "91340": { lat: 34.2881, lng: -118.4386 }, // San Fernando, CA
            "91342": { lat: 34.3231, lng: -118.4257 }, // Sylmar, CA
            "91343": { lat: 34.2351, lng: -118.4644 }, // North Hills, CA
            "91344": { lat: 34.2881, lng: -118.5031 }, // Granada Hills, CA
            "91345": { lat: 34.2881, lng: -118.4644 }, // Mission Hills, CA
            "91350": { lat: 34.4531, lng: -118.5257 }, // Santa Clarita, CA
            "91351": { lat: 34.4531, lng: -118.5257 }, // Santa Clarita, CA
            "91352": { lat: 34.2351, lng: -118.3870 }, // Sun Valley, CA
            "91354": { lat: 34.4531, lng: -118.5257 }, // Santa Clarita, CA
            "91355": { lat: 34.4531, lng: -118.5644 }, // Santa Clarita, CA
            "91356": { lat: 34.1517, lng: -118.5386 }, // Tarzana, CA
            "91360": { lat: 34.1700, lng: -118.8500 }, // Thousand Oaks, CA
            "91361": { lat: 34.1478, lng: -118.8185 }, // Westlake Village, CA
            "91362": { lat: 34.1700, lng: -118.8500 }, // Thousand Oaks, CA
            "91364": { lat: 34.1517, lng: -118.5644 }, // Woodland Hills, CA
            "91367": { lat: 34.1517, lng: -118.6031 }, // Woodland Hills, CA
            "91401": { lat: 34.1794, lng: -118.4386 }, // Van Nuys, CA
            "91402": { lat: 34.2351, lng: -118.4386 }, // Panorama City, CA
            "91403": { lat: 34.1517, lng: -118.4644 }, // Sherman Oaks, CA
            "91405": { lat: 34.1794, lng: -118.4386 }, // Van Nuys, CA
            "91406": { lat: 34.1794, lng: -118.5031 }, // Van Nuys, CA
            "91411": { lat: 34.1794, lng: -118.4644 }, // Van Nuys, CA
            "91423": { lat: 34.1517, lng: -118.4386 }, // Sherman Oaks, CA
            "91436": { lat: 34.1517, lng: -118.4902 }, // Encino, CA
            
            // Las Vegas
            "89101": { lat: 36.1750, lng: -115.1372 }, // Las Vegas, NV
            "89102": { lat: 36.1750, lng: -115.1933 }, // Las Vegas, NV
            "89103": { lat: 36.1147, lng: -115.1933 }, // Las Vegas, NV
            "89104": { lat: 36.1750, lng: -115.0811 }, // Las Vegas, NV
            "89106": { lat: 36.2075, lng: -115.1653 }, // Las Vegas, NV
            "89107": { lat: 36.1750, lng: -115.2214 }, // Las Vegas, NV
            "89108": { lat: 36.2075, lng: -115.2214 }, // Las Vegas, NV
            "89109": { lat: 36.1147, lng: -115.1653 }, // Las Vegas, NV (The Strip)
            "89110": { lat: 36.1750, lng: -115.0531 }, // Las Vegas, NV
            "89113": { lat: 36.0869, lng: -115.2495 }, // Las Vegas, NV
            "89117": { lat: 36.1425, lng: -115.2775 }, // Las Vegas, NV

            // San Diego County - Poway
            "92064": { lat: 32.9628, lng: -117.0359 }, // Poway, CA

            // Orange County - San Juan Capistrano
            "92675": { lat: 33.5017, lng: -117.6628 }, // San Juan Capistrano, CA

            // Silicon Valley Area
            "94301": { lat: 37.4419, lng: -122.1430 }, // Palo Alto, CA
            "94302": { lat: 37.4419, lng: -122.1430 }, // Palo Alto, CA
            "94303": { lat: 37.4419, lng: -122.1430 }, // Palo Alto, CA
            "94304": { lat: 37.4419, lng: -122.1430 }, // Palo Alto, CA
            "94305": { lat: 37.4419, lng: -122.1430 }, // Stanford, CA
            "94306": { lat: 37.4419, lng: -122.1430 }, // Palo Alto, CA
            "95014": { lat: 37.3230, lng: -122.0322 }, // Cupertino, CA
            "95051": { lat: 37.3688, lng: -121.9678 }, // Santa Clara, CA
            "95054": { lat: 37.3688, lng: -121.9678 }, // Santa Clara, CA
            "95070": { lat: 37.3541, lng: -122.0522 }, // Saratoga, CA
            "95110": { lat: 37.3382, lng: -121.8863 }, // San Jose, CA
            "95111": { lat: 37.3382, lng: -121.8863 }, // San Jose, CA
            "95112": { lat: 37.3382, lng: -121.8863 }, // San Jose, CA
            "95113": { lat: 37.3382, lng: -121.8863 }, // San Jose, CA
            "95116": { lat: 37.3382, lng: -121.8863 }, // San Jose, CA
            "95117": { lat: 37.3382, lng: -121.8863 }, // San Jose, CA
            "95118": { lat: 37.3382, lng: -121.8863 }, // San Jose, CA
            "95119": { lat: 37.3382, lng: -121.8863 }, // San Jose, CA
            "95120": { lat: 37.3382, lng: -121.8863 }, // San Jose, CA
            "95121": { lat: 37.3382, lng: -121.8863 }, // San Jose, CA
            "95122": { lat: 37.3382, lng: -121.8863 }, // San Jose, CA
            "95123": { lat: 37.3382, lng: -121.8863 }, // San Jose, CA
            "95124": { lat: 37.3382, lng: -121.8863 }, // San Jose, CA
            "95125": { lat: 37.3382, lng: -121.8863 }, // San Jose, CA
            "95126": { lat: 37.3382, lng: -121.8863 }, // San Jose, CA
            "95127": { lat: 37.3382, lng: -121.8863 }, // San Jose, CA
            "95128": { lat: 37.3382, lng: -121.8863 }, // San Jose, CA
            "95129": { lat: 37.3382, lng: -121.8863 }, // San Jose, CA
            "95130": { lat: 37.3382, lng: -121.8863 }, // San Jose, CA
            "95131": { lat: 37.3382, lng: -121.8863 }, // San Jose, CA
            "95132": { lat: 37.3382, lng: -121.8863 }, // San Jose, CA
            "95133": { lat: 37.3382, lng: -121.8863 }, // San Jose, CA
            "95134": { lat: 37.3382, lng: -121.8863 }, // San Jose, CA
            "95135": { lat: 37.3382, lng: -121.8863 }, // San Jose, CA
            "95136": { lat: 37.3382, lng: -121.8863 }, // San Jose, CA
            "95138": { lat: 37.3382, lng: -121.8863 }, // San Jose, CA
            "95139": { lat: 37.3382, lng: -121.8863 }, // San Jose, CA
            "94085": { lat: 37.4224, lng: -122.0856 }, // Sunnyvale, CA
            "94086": { lat: 37.4224, lng: -122.0856 }, // Sunnyvale, CA
            "94087": { lat: 37.4224, lng: -122.0856 }, // Sunnyvale, CA
            "94089": { lat: 37.4224, lng: -122.0856 }, // Sunnyvale, CA
            "94041": { lat: 37.3861, lng: -122.0839 }, // Mountain View, CA
            "94043": { lat: 37.3861, lng: -122.0839 }, // Mountain View, CA
            "94024": { lat: 37.4852, lng: -122.1483 }, // Los Altos, CA
            "94022": { lat: 37.4852, lng: -122.1483 }, // Los Altos, CA
            "94040": { lat: 37.3861, lng: -122.0839 }, // Mountain View, CA
            "94035": { lat: 37.4419, lng: -122.1430 }, // Milpitas, CA
            "95035": { lat: 37.4323, lng: -121.9018 }, // Milpitas, CA

            // Ventura County
            "93001": { lat: 34.2746, lng: -119.2290 }, // Ventura, CA
            "93003": { lat: 34.2746, lng: -119.2290 }, // Ventura, CA
            "93004": { lat: 34.2746, lng: -119.2290 }, // Ventura, CA
            "93006": { lat: 34.2746, lng: -119.2290 }, // Ventura, CA
            "93009": { lat: 34.2746, lng: -119.2290 }, // Ventura, CA

            // Seattle Metropolitan Area
            // Seattle
            "98101": { lat: 47.6062, lng: -122.3321 }, // Seattle, WA (Downtown)
            "98102": { lat: 47.6205, lng: -122.3212 }, // Seattle, WA (Capitol Hill)
            "98103": { lat: 47.6740, lng: -122.3419 }, // Seattle, WA (Fremont/Wallingford)
            "98104": { lat: 47.6021, lng: -122.3321 }, // Seattle, WA (Pioneer Square)
            "98105": { lat: 47.6606, lng: -122.3031 }, // Seattle, WA (University District)
            "98106": { lat: 47.5287, lng: -122.3487 }, // Seattle, WA (White Center)
            "98107": { lat: 47.6684, lng: -122.3762 }, // Seattle, WA (Ballard)
            "98108": { lat: 47.5287, lng: -122.3212 }, // Seattle, WA (South Park)
            "98109": { lat: 47.6205, lng: -122.3487 }, // Seattle, WA (South Lake Union)
            "98112": { lat: 47.6205, lng: -122.3031 }, // Seattle, WA (Capitol Hill East)
            "98115": { lat: 47.6897, lng: -122.3031 }, // Seattle, WA (Northgate)
            "98116": { lat: 47.5649, lng: -122.3762 }, // Seattle, WA (West Seattle)
            "98117": { lat: 47.6897, lng: -122.3762 }, // Seattle, WA (Ballard North)
            "98118": { lat: 47.5287, lng: -122.2762 }, // Seattle, WA (Rainier Valley)
            "98119": { lat: 47.6354, lng: -122.3762 }, // Seattle, WA (Interbay)
            "98121": { lat: 47.6149, lng: -122.3487 }, // Seattle, WA (Belltown)
            "98122": { lat: 47.6062, lng: -122.3031 }, // Seattle, WA (Central District)
            "98125": { lat: 47.7231, lng: -122.3031 }, // Seattle, WA (Northgate North)
            "98126": { lat: 47.5649, lng: -122.3487 }, // Seattle, WA (West Seattle South)
            "98133": { lat: 47.7231, lng: -122.3487 }, // Seattle, WA (North Seattle)
            "98134": { lat: 47.5649, lng: -122.3212 }, // Seattle, WA (SODO)
            "98136": { lat: 47.5287, lng: -122.3762 }, // Seattle, WA (Burien/Highline)
            "98144": { lat: 47.5649, lng: -122.2762 }, // Seattle, WA (Mount Baker)
            "98146": { lat: 47.5287, lng: -122.3487 }, // Seattle, WA (Burien)
            "98148": { lat: 47.4287, lng: -122.3212 }, // Seattle, WA (SeaTac)
            "98154": { lat: 47.5649, lng: -122.3321 }, // Seattle, WA (South Seattle)
            "98155": { lat: 47.7231, lng: -122.2762 }, // Seattle, WA (Shoreline)
            "98158": { lat: 47.4649, lng: -122.2762 }, // Seattle, WA (Tukwila)
            "98164": { lat: 47.5649, lng: -122.3321 }, // Seattle, WA (Industrial District)
            "98166": { lat: 47.4649, lng: -122.3487 }, // Seattle, WA (Normandy Park)
            "98168": { lat: 47.4649, lng: -122.3212 }, // Seattle, WA (Tukwila South)
            "98177": { lat: 47.7564, lng: -122.3762 }, // Seattle, WA (Shoreline West)
            "98178": { lat: 47.4649, lng: -122.2762 }, // Seattle, WA (Tukwila East)
            "98188": { lat: 47.4287, lng: -122.2762 }, // Seattle, WA (SeaTac East)
            "98199": { lat: 47.6354, lng: -122.4031 }, // Seattle, WA (Magnolia)

            // Redmond
            "98052": { lat: 47.6740, lng: -122.1215 }, // Redmond, WA
            "98053": { lat: 47.6740, lng: -122.1215 }, // Redmond, WA (Microsoft Campus area)

            // Bellevue
            "98004": { lat: 47.6101, lng: -122.2015 }, // Bellevue, WA (Downtown)
            "98005": { lat: 47.6101, lng: -122.1515 }, // Bellevue, WA (East)
            "98006": { lat: 47.5649, lng: -122.1515 }, // Bellevue, WA (South)
            "98007": { lat: 47.6101, lng: -122.1215 }, // Bellevue, WA (Crossroads)
            "98008": { lat: 47.6354, lng: -122.1515 }, // Bellevue, WA (North)

            // Medina
            "98039": { lat: 47.6240, lng: -122.2304 }, // Medina, WA

            // Florida - Fort Myers Area (Lee County)
            "33901": { lat: 26.6406, lng: -81.8723 }, // Fort Myers, FL (Downtown)
            "33902": { lat: 26.6406, lng: -81.8723 }, // Fort Myers, FL
            "33903": { lat: 26.6406, lng: -81.8723 }, // Fort Myers, FL
            "33904": { lat: 26.6406, lng: -81.8723 }, // Fort Myers, FL
            "33905": { lat: 26.6406, lng: -81.8723 }, // Fort Myers, FL
            "33906": { lat: 26.6406, lng: -81.8723 }, // Fort Myers, FL
            "33907": { lat: 26.6406, lng: -81.8723 }, // Fort Myers, FL
            "33908": { lat: 26.6406, lng: -81.8723 }, // Fort Myers, FL
            "33909": { lat: 26.6406, lng: -81.8723 }, // Fort Myers, FL
            "33912": { lat: 26.6406, lng: -81.8723 }, // Fort Myers, FL
            "33913": { lat: 26.6406, lng: -81.8723 }, // Fort Myers, FL
            "33914": { lat: 26.6406, lng: -81.8723 }, // Fort Myers, FL
            "33915": { lat: 26.6406, lng: -81.8723 }, // Fort Myers, FL
            "33916": { lat: 26.6406, lng: -81.8723 }, // Fort Myers, FL
            "33917": { lat: 26.6406, lng: -81.8723 }, // Fort Myers, FL
            "33918": { lat: 26.6406, lng: -81.8723 }, // Fort Myers, FL
            "33919": { lat: 26.6406, lng: -81.8723 }, // Fort Myers, FL
            "33920": { lat: 26.6406, lng: -81.8723 }, // Fort Myers, FL
            "33921": { lat: 26.5628, lng: -81.9495 }, // Estero, FL
            "33922": { lat: 26.4614, lng: -81.8081 }, // Fort Myers Beach, FL
            "33924": { lat: 26.4614, lng: -81.8081 }, // Fort Myers Beach, FL
            "33928": { lat: 26.5312, lng: -82.0251 }, // Bonita Springs, FL
            "33931": { lat: 26.3587, lng: -81.8723 }, // Lehigh Acres, FL
            "33936": { lat: 26.3587, lng: -81.8723 }, // Lehigh Acres, FL
            "33957": { lat: 26.7153, lng: -81.7787 }, // North Fort Myers, FL
            "33966": { lat: 26.4801, lng: -81.7787 }, // Cape Coral, FL
            "33967": { lat: 26.4801, lng: -81.7787 }, // Cape Coral, FL
            "33971": { lat: 26.4801, lng: -81.7787 }, // Cape Coral, FL
            "33972": { lat: 26.4801, lng: -81.7787 }, // Cape Coral, FL
            "33973": { lat: 26.4801, lng: -81.7787 }, // Cape Coral, FL
            "33974": { lat: 26.4801, lng: -81.7787 }, // Cape Coral, FL
            "33976": { lat: 26.4801, lng: -81.7787 }, // Cape Coral, FL
            "33990": { lat: 26.4801, lng: -81.7787 }, // Cape Coral, FL
            "33991": { lat: 26.4801, lng: -81.7787 }, // Cape Coral, FL
            "33993": { lat: 26.4801, lng: -81.7787 }, // Cape Coral, FL

            // Florida - Sarasota Area (Sarasota County)
            "34201": { lat: 27.4989, lng: -82.5748 }, // Bradenton, FL
            "34202": { lat: 27.4989, lng: -82.5748 }, // Bradenton, FL
            "34203": { lat: 27.4989, lng: -82.5748 }, // Bradenton, FL
            "34205": { lat: 27.4989, lng: -82.5748 }, // Bradenton, FL
            "34207": { lat: 27.4989, lng: -82.5748 }, // Bradenton, FL
            "34208": { lat: 27.4989, lng: -82.5748 }, // Bradenton, FL
            "34209": { lat: 27.4989, lng: -82.5748 }, // Bradenton, FL
            "34210": { lat: 27.4989, lng: -82.5748 }, // Bradenton, FL
            "34211": { lat: 27.4989, lng: -82.5748 }, // Bradenton, FL
            "34212": { lat: 27.4989, lng: -82.5748 }, // Bradenton, FL
            "34215": { lat: 27.4989, lng: -82.5748 }, // Bradenton, FL
            "34216": { lat: 27.4989, lng: -82.5748 }, // Bradenton, FL
            "34217": { lat: 27.4989, lng: -82.5748 }, // Bradenton, FL
            "34219": { lat: 27.4989, lng: -82.5748 }, // Bradenton, FL
            "34221": { lat: 27.4989, lng: -82.5748 }, // Bradenton, FL
            "34222": { lat: 27.4989, lng: -82.5748 }, // Bradenton, FL
            "34223": { lat: 27.4989, lng: -82.5748 }, // Bradenton, FL
            "34224": { lat: 27.4989, lng: -82.5748 }, // Bradenton, FL
            "34228": { lat: 27.4989, lng: -82.5748 }, // Bradenton, FL
            "34229": { lat: 27.4989, lng: -82.5748 }, // Bradenton, FL
            "34230": { lat: 27.3364, lng: -82.5307 }, // Sarasota, FL
            "34231": { lat: 27.3364, lng: -82.5307 }, // Sarasota, FL
            "34232": { lat: 27.3364, lng: -82.5307 }, // Sarasota, FL
            "34233": { lat: 27.3364, lng: -82.5307 }, // Sarasota, FL
            "34234": { lat: 27.3364, lng: -82.5307 }, // Sarasota, FL
            "34235": { lat: 27.3364, lng: -82.5307 }, // Sarasota, FL
            "34236": { lat: 27.3364, lng: -82.5307 }, // Sarasota, FL
            "34237": { lat: 27.3364, lng: -82.5307 }, // Sarasota, FL
            "34238": { lat: 27.3364, lng: -82.5307 }, // Sarasota, FL
            "34239": { lat: 27.3364, lng: -82.5307 }, // Sarasota, FL
            "34240": { lat: 27.3364, lng: -82.5307 }, // Sarasota, FL
            "34241": { lat: 27.3364, lng: -82.5307 }, // Sarasota, FL
            "34242": { lat: 27.3364, lng: -82.5307 }, // Sarasota, FL
            "34243": { lat: 27.3364, lng: -82.5307 }, // Sarasota, FL
            "34251": { lat: 27.3364, lng: -82.5307 }, // Sarasota, FL
            "34260": { lat: 27.3364, lng: -82.5307 }, // Sarasota, FL
            "34264": { lat: 27.3364, lng: -82.5307 }, // Sarasota, FL
            "34265": { lat: 27.3364, lng: -82.5307 }, // Sarasota, FL
            "34266": { lat: 27.3364, lng: -82.5307 }, // Sarasota, FL
            "34267": { lat: 27.3364, lng: -82.5307 }, // Sarasota, FL
            "34268": { lat: 27.3364, lng: -82.5307 }, // Sarasota, FL
            "34269": { lat: 27.3364, lng: -82.5307 }, // Sarasota, FL
            "34270": { lat: 27.3364, lng: -82.5307 }, // Sarasota, FL
            "34274": { lat: 27.3364, lng: -82.5307 }, // Sarasota, FL
            "34275": { lat: 27.3364, lng: -82.5307 }, // Sarasota, FL
            "34276": { lat: 27.3364, lng: -82.5307 }, // Sarasota, FL
            "34277": { lat: 27.3364, lng: -82.5307 }, // Sarasota, FL
            "34278": { lat: 27.3364, lng: -82.5307 }, // Sarasota, FL
            "34280": { lat: 27.3364, lng: -82.5307 }, // Sarasota, FL
            "34281": { lat: 27.3364, lng: -82.5307 }, // Sarasota, FL
            "34282": { lat: 27.3364, lng: -82.5307 }, // Sarasota, FL
            "34284": { lat: 27.3364, lng: -82.5307 }, // Sarasota, FL
            "34285": { lat: 27.3364, lng: -82.5307 }, // Sarasota, FL
            "34286": { lat: 27.3364, lng: -82.5307 }, // Sarasota, FL
            "34287": { lat: 27.3364, lng: -82.5307 }, // Sarasota, FL
            "34288": { lat: 27.3364, lng: -82.5307 }, // Sarasota, FL
            "34289": { lat: 27.3364, lng: -82.5307 }, // Sarasota, FL
            "34290": { lat: 27.3364, lng: -82.5307 }, // Sarasota, FL
            "34291": { lat: 27.3364, lng: -82.5307 }, // Sarasota, FL
            "34292": { lat: 27.3364, lng: -82.5307 }, // Sarasota, FL
            "34293": { lat: 27.3364, lng: -82.5307 }, // Sarasota, FL
            "34295": { lat: 27.3364, lng: -82.5307 }, // Sarasota, FL

            // Florida - Clearwater/Tampa Bay Area (Pinellas County)
            "33755": { lat: 27.9659, lng: -82.8001 }, // Clearwater, FL
            "33756": { lat: 27.9659, lng: -82.8001 }, // Clearwater, FL
            "33759": { lat: 27.9659, lng: -82.8001 }, // Clearwater, FL
            "33760": { lat: 27.9659, lng: -82.8001 }, // Clearwater, FL
            "33761": { lat: 27.9659, lng: -82.8001 }, // Clearwater, FL
            "33762": { lat: 27.9659, lng: -82.8001 }, // Clearwater, FL
            "33763": { lat: 27.9659, lng: -82.8001 }, // Clearwater, FL
            "33764": { lat: 27.9659, lng: -82.8001 }, // Clearwater, FL
            "33765": { lat: 27.9659, lng: -82.8001 }, // Clearwater, FL
            "33767": { lat: 27.9659, lng: -82.8001 }, // Clearwater, FL
            "33770": { lat: 27.9659, lng: -82.8001 }, // Clearwater, FL
            "33771": { lat: 27.9659, lng: -82.8001 }, // Clearwater, FL
            "33772": { lat: 27.9659, lng: -82.8001 }, // Clearwater, FL
            "33773": { lat: 27.9659, lng: -82.8001 }, // Clearwater, FL
            "33774": { lat: 27.9659, lng: -82.8001 }, // Clearwater, FL
            "33775": { lat: 27.9659, lng: -82.8001 }, // Clearwater, FL
            "33776": { lat: 27.9659, lng: -82.8001 }, // Clearwater, FL
            "33777": { lat: 27.9659, lng: -82.8001 }, // Clearwater, FL
            "33778": { lat: 27.9659, lng: -82.8001 }, // Clearwater, FL
            "33779": { lat: 27.9659, lng: -82.8001 }, // Clearwater, FL
            "33780": { lat: 27.9659, lng: -82.8001 }, // Clearwater, FL
            "33781": { lat: 27.9659, lng: -82.8001 }, // Clearwater, FL
            "33782": { lat: 27.9659, lng: -82.8001 }, // Clearwater, FL
            "33784": { lat: 27.9659, lng: -82.8001 }, // Clearwater, FL
            "33785": { lat: 27.9659, lng: -82.8001 }, // Clearwater, FL
            "33786": { lat: 27.9659, lng: -82.8001 }, // Clearwater, FL
            "33787": { lat: 27.9659, lng: -82.8001 }, // Clearwater, FL

            // San Francisco, CA
            "94102": { lat: 37.7749, lng: -122.4194 }, // San Francisco, CA (Downtown/Civic Center)
            "94103": { lat: 37.7749, lng: -122.4194 }, // San Francisco, CA (SOMA)
            "94104": { lat: 37.7749, lng: -122.4194 }, // San Francisco, CA (Financial District)
            "94105": { lat: 37.7749, lng: -122.4194 }, // San Francisco, CA (SOMA/Rincon Hill)
            "94107": { lat: 37.7749, lng: -122.4194 }, // San Francisco, CA (Potrero Hill)
            "94108": { lat: 37.7749, lng: -122.4194 }, // San Francisco, CA (Chinatown/Nob Hill)
            "94109": { lat: 37.7749, lng: -122.4194 }, // San Francisco, CA (Polk Gulch/Russian Hill)
            "94110": { lat: 37.7749, lng: -122.4194 }, // San Francisco, CA (Mission District)
            "94111": { lat: 37.7749, lng: -122.4194 }, // San Francisco, CA (Financial District)
            "94112": { lat: 37.7749, lng: -122.4194 }, // San Francisco, CA (Outer Mission)
            "94114": { lat: 37.7749, lng: -122.4194 }, // San Francisco, CA (Castro/Noe Valley)
            "94115": { lat: 37.7749, lng: -122.4194 }, // San Francisco, CA (Pacific Heights)
            "94116": { lat: 37.7749, lng: -122.4194 }, // San Francisco, CA (Sunset District)
            "94117": { lat: 37.7749, lng: -122.4194 }, // San Francisco, CA (Haight-Ashbury)
            "94118": { lat: 37.7749, lng: -122.4194 }, // San Francisco, CA (Richmond District)
            "94121": { lat: 37.7749, lng: -122.4194 }, // San Francisco, CA (Richmond District)
            "94122": { lat: 37.7749, lng: -122.4194 }, // San Francisco, CA (Sunset District)
            "94123": { lat: 37.7749, lng: -122.4194 }, // San Francisco, CA (Marina District)
            "94124": { lat: 37.7749, lng: -122.4194 }, // San Francisco, CA (Bayview)
            "94127": { lat: 37.7749, lng: -122.4194 }, // San Francisco, CA (West Portal)
            "94129": { lat: 37.7749, lng: -122.4194 }, // San Francisco, CA (Presidio)
            "94130": { lat: 37.7749, lng: -122.4194 }, // San Francisco, CA (Treasure Island)
            "94131": { lat: 37.7749, lng: -122.4194 }, // San Francisco, CA (Glen Park)
            "94132": { lat: 37.7749, lng: -122.4194 }, // San Francisco, CA (Lake Merced)
            "94133": { lat: 37.7749, lng: -122.4194 }, // San Francisco, CA (North Beach)
            "94134": { lat: 37.7749, lng: -122.4194 }, // San Francisco, CA (Visitacion Valley)
            "94158": { lat: 37.7749, lng: -122.4194 }, // San Francisco, CA (Mission Bay)

            // Additional Major US Cities ZIP Codes

            // Los Angeles, CA (Additional ZIP codes)
            "90001": { lat: 34.0522, lng: -118.2437 }, // Los Angeles, CA (South LA)
            "90002": { lat: 34.0522, lng: -118.2437 }, // Los Angeles, CA (Watts)
            "90003": { lat: 34.0522, lng: -118.2437 }, // Los Angeles, CA (South LA)
            "90004": { lat: 34.0522, lng: -118.2437 }, // Los Angeles, CA (Mid-Wilshire)
            "90005": { lat: 34.0522, lng: -118.2437 }, // Los Angeles, CA (Koreatown)
            "90006": { lat: 34.0522, lng: -118.2437 }, // Los Angeles, CA (Koreatown)
            "90007": { lat: 34.0522, lng: -118.2437 }, // Los Angeles, CA (USC area)
            "90008": { lat: 34.0522, lng: -118.2437 }, // Los Angeles, CA (Baldwin Hills)
            "90010": { lat: 34.0522, lng: -118.2437 }, // Los Angeles, CA (Mid-Wilshire)
            "90011": { lat: 34.0522, lng: -118.2437 }, // Los Angeles, CA (South LA)
            "90012": { lat: 34.0522, lng: -118.2437 }, // Los Angeles, CA (Downtown)
            "90013": { lat: 34.0522, lng: -118.2437 }, // Los Angeles, CA (Downtown)
            "90014": { lat: 34.0522, lng: -118.2437 }, // Los Angeles, CA (Downtown)
            "90015": { lat: 34.0522, lng: -118.2437 }, // Los Angeles, CA (Downtown)
            "90016": { lat: 34.0522, lng: -118.2437 }, // Los Angeles, CA (Mid-City)
            "90017": { lat: 34.0522, lng: -118.2437 }, // Los Angeles, CA (Downtown)
            "90018": { lat: 34.0522, lng: -118.2437 }, // Los Angeles, CA (Mid-City)
            "90019": { lat: 34.0522, lng: -118.2437 }, // Los Angeles, CA (Mid-City)
            "90020": { lat: 34.0522, lng: -118.2437 }, // Los Angeles, CA (Koreatown)
            "90021": { lat: 34.0522, lng: -118.2437 }, // Los Angeles, CA (Downtown)
            "90022": { lat: 34.0522, lng: -118.2437 }, // Los Angeles, CA (Boyle Heights)
            "90023": { lat: 34.0522, lng: -118.2437 }, // Los Angeles, CA (Boyle Heights)
            "90024": { lat: 34.0522, lng: -118.2437 }, // Los Angeles, CA (Westwood)
            "90025": { lat: 34.0522, lng: -118.2437 }, // Los Angeles, CA (West LA)
            "90026": { lat: 34.0522, lng: -118.2437 }, // Los Angeles, CA (Silver Lake)
            "90027": { lat: 34.0522, lng: -118.2437 }, // Los Angeles, CA (Los Feliz)
            "90028": { lat: 34.0522, lng: -118.2437 }, // Los Angeles, CA (Hollywood)
            "90029": { lat: 34.0522, lng: -118.2437 }, // Los Angeles, CA (East Hollywood)
            "90031": { lat: 34.0522, lng: -118.2437 }, // Los Angeles, CA (Lincoln Heights)
            "90032": { lat: 34.0522, lng: -118.2437 }, // Los Angeles, CA (El Sereno)
            "90033": { lat: 34.0522, lng: -118.2437 }, // Los Angeles, CA (Boyle Heights)
            "90034": { lat: 34.0522, lng: -118.2437 }, // Los Angeles, CA (Mid-City)
            "90035": { lat: 34.0522, lng: -118.2437 }, // Los Angeles, CA (Pico-Robertson)
            "90036": { lat: 34.0522, lng: -118.2437 }, // Los Angeles, CA (Mid-Wilshire)
            "90037": { lat: 34.0522, lng: -118.2437 }, // Los Angeles, CA (South LA)
            "90038": { lat: 34.0522, lng: -118.2437 }, // Los Angeles, CA (Hollywood)
            "90039": { lat: 34.0522, lng: -118.2437 }, // Los Angeles, CA (Atwater Village)
            "90040": { lat: 34.0522, lng: -118.2437 }, // Los Angeles, CA (Commerce)
            "90041": { lat: 34.0522, lng: -118.2437 }, // Los Angeles, CA (Eagle Rock)
            "90042": { lat: 34.0522, lng: -118.2437 }, // Los Angeles, CA (Highland Park)
            "90043": { lat: 34.0522, lng: -118.2437 }, // Los Angeles, CA (Leimert Park)
            "90044": { lat: 34.0522, lng: -118.2437 }, // Los Angeles, CA (South LA)
            "90045": { lat: 34.0522, lng: -118.2437 }, // Los Angeles, CA (Westchester)
            "90046": { lat: 34.0522, lng: -118.2437 }, // Los Angeles, CA (West Hollywood)
            "90047": { lat: 34.0522, lng: -118.2437 }, // Los Angeles, CA (South LA)
            "90048": { lat: 34.0522, lng: -118.2437 }, // Los Angeles, CA (Beverly Grove)
            "90049": { lat: 34.0522, lng: -118.2437 }, // Los Angeles, CA (Brentwood)
            "90056": { lat: 34.0522, lng: -118.2437 }, // Los Angeles, CA (Ladera Heights)
            "90057": { lat: 34.0522, lng: -118.2437 }, // Los Angeles, CA (Westlake)
            "90058": { lat: 34.0522, lng: -118.2437 }, // Los Angeles, CA (Vernon)
            "90059": { lat: 34.0522, lng: -118.2437 }, // Los Angeles, CA (Watts)
            "90061": { lat: 34.0522, lng: -118.2437 }, // Los Angeles, CA (South LA)
            "90062": { lat: 34.0522, lng: -118.2437 }, // Los Angeles, CA (South LA)
            "90063": { lat: 34.0522, lng: -118.2437 }, // Los Angeles, CA (Boyle Heights)
            "90064": { lat: 34.0522, lng: -118.2437 }, // Los Angeles, CA (West LA)
            "90065": { lat: 34.0522, lng: -118.2437 }, // Los Angeles, CA (Glassell Park)
            "90066": { lat: 34.0522, lng: -118.2437 }, // Los Angeles, CA (Mar Vista)
            "90067": { lat: 34.0522, lng: -118.2437 }, // Los Angeles, CA (Century City)
            "90068": { lat: 34.0522, lng: -118.2437 }, // Los Angeles, CA (Hollywood Hills)
            "90069": { lat: 34.0522, lng: -118.2437 }, // Los Angeles, CA (West Hollywood)
            "90071": { lat: 34.0522, lng: -118.2437 }, // Los Angeles, CA (Downtown)
            "90077": { lat: 34.0522, lng: -118.2437 }, // Los Angeles, CA (Bel Air)
            "90089": { lat: 34.0522, lng: -118.2437 }, // Los Angeles, CA (USC)
            "90210": { lat: 34.0522, lng: -118.2437 }, // Beverly Hills, CA
            "90211": { lat: 34.0522, lng: -118.2437 }, // Beverly Hills, CA
            "90212": { lat: 34.0522, lng: -118.2437 }, // Beverly Hills, CA
            "90401": { lat: 34.0195, lng: -118.4912 }, // Santa Monica, CA
            "90402": { lat: 34.0195, lng: -118.4912 }, // Santa Monica, CA
            "90403": { lat: 34.0195, lng: -118.4912 }, // Santa Monica, CA
            "90404": { lat: 34.0195, lng: -118.4912 }, // Santa Monica, CA
            "90405": { lat: 34.0195, lng: -118.4912 }, // Santa Monica, CA

            // Houston, TX
            "77001": { lat: 29.7604, lng: -95.3698 }, // Houston, TX (Downtown)
            "77002": { lat: 29.7604, lng: -95.3698 }, // Houston, TX (Downtown)
            "77003": { lat: 29.7604, lng: -95.3698 }, // Houston, TX (Third Ward)
            "77004": { lat: 29.7604, lng: -95.3698 }, // Houston, TX (Museum District)
            "77005": { lat: 29.7604, lng: -95.3698 }, // Houston, TX (Rice University)
            "77006": { lat: 29.7604, lng: -95.3698 }, // Houston, TX (Montrose)
            "77007": { lat: 29.7604, lng: -95.3698 }, // Houston, TX (Heights)
            "77008": { lat: 29.7604, lng: -95.3698 }, // Houston, TX (Heights)
            "77009": { lat: 29.7604, lng: -95.3698 }, // Houston, TX (Near Northside)
            "77010": { lat: 29.7604, lng: -95.3698 }, // Houston, TX (Medical Center)
            "77011": { lat: 29.7604, lng: -95.3698 }, // Houston, TX (East End)
            "77012": { lat: 29.7604, lng: -95.3698 }, // Houston, TX (East End)
            "77013": { lat: 29.7604, lng: -95.3698 }, // Houston, TX (East Houston)
            "77014": { lat: 29.7604, lng: -95.3698 }, // Houston, TX (Humble)
            "77015": { lat: 29.7604, lng: -95.3698 }, // Houston, TX (Channelview)
            "77016": { lat: 29.7604, lng: -95.3698 }, // Houston, TX (Champions)
            "77017": { lat: 29.7604, lng: -95.3698 }, // Houston, TX (South Houston)
            "77018": { lat: 29.7604, lng: -95.3698 }, // Houston, TX (Heights)
            "77019": { lat: 29.7604, lng: -95.3698 }, // Houston, TX (River Oaks)
            "77020": { lat: 29.7604, lng: -95.3698 }, // Houston, TX (Fifth Ward)
            "77021": { lat: 29.7604, lng: -95.3698 }, // Houston, TX (Third Ward)
            "77022": { lat: 29.7604, lng: -95.3698 }, // Houston, TX (Near Northside)
            "77023": { lat: 29.7604, lng: -95.3698 }, // Houston, TX (East End)
            "77024": { lat: 29.7604, lng: -95.3698 }, // Houston, TX (Memorial)
            "77025": { lat: 29.7604, lng: -95.3698 }, // Houston, TX (Bellaire)
            "77026": { lat: 29.7604, lng: -95.3698 }, // Houston, TX (Kashmere Gardens)
            "77027": { lat: 29.7604, lng: -95.3698 }, // Houston, TX (River Oaks)
            "77028": { lat: 29.7604, lng: -95.3698 }, // Houston, TX (Denver Harbor)
            "77029": { lat: 29.7604, lng: -95.3698 }, // Houston, TX (Magnolia Park)
            "77030": { lat: 29.7604, lng: -95.3698 }, // Houston, TX (Medical Center)
            "77031": { lat: 29.7604, lng: -95.3698 }, // Houston, TX (Gulfton)
            "77032": { lat: 29.7604, lng: -95.3698 }, // Houston, TX (Aldine)
            "77033": { lat: 29.7604, lng: -95.3698 }, // Houston, TX (South Park)
            "77034": { lat: 29.7604, lng: -95.3698 }, // Houston, TX (Clear Lake)
            "77035": { lat: 29.7604, lng: -95.3698 }, // Houston, TX (Meyerland)
            "77036": { lat: 29.7604, lng: -95.3698 }, // Houston, TX (Sharpstown)
            "77037": { lat: 29.7604, lng: -95.3698 }, // Houston, TX (Acres Homes)
            "77038": { lat: 29.7604, lng: -95.3698 }, // Houston, TX (Humble)
            "77039": { lat: 29.7604, lng: -95.3698 }, // Houston, TX (Aldine)
            "77040": { lat: 29.7604, lng: -95.3698 }, // Houston, TX (Northwest Houston)
            "77041": { lat: 29.7604, lng: -95.3698 }, // Houston, TX (Spring Branch)
            "77042": { lat: 29.7604, lng: -95.3698 }, // Houston, TX (Westchase)
            "77043": { lat: 29.7604, lng: -95.3698 }, // Houston, TX (Spring Branch)
            "77044": { lat: 29.7604, lng: -95.3698 }, // Houston, TX (East Houston)
            "77045": { lat: 29.7604, lng: -95.3698 }, // Houston, TX (Airport)
            "77046": { lat: 29.7604, lng: -95.3698 }, // Houston, TX (Galleria)
            "77047": { lat: 29.7604, lng: -95.3698 }, // Houston, TX (Hobby Airport)
            "77048": { lat: 29.7604, lng: -95.3698 }, // Houston, TX (Fondren Southwest)
            "77049": { lat: 29.7604, lng: -95.3698 }, // Houston, TX (Greenspoint)
            "77050": { lat: 29.7604, lng: -95.3698 }, // Houston, TX (Hobby Airport)
            "77051": { lat: 29.7604, lng: -95.3698 }, // Houston, TX (Sunnyside)
            "77052": { lat: 29.7604, lng: -95.3698 }, // Houston, TX (Galleria)
            "77053": { lat: 29.7604, lng: -95.3698 }, // Houston, TX (Southwest Houston)
            "77054": { lat: 29.7604, lng: -95.3698 }, // Houston, TX (Medical Center)
            "77055": { lat: 29.7604, lng: -95.3698 }, // Houston, TX (Memorial)
            "77056": { lat: 29.7604, lng: -95.3698 }, // Houston, TX (Galleria)
            "77057": { lat: 29.7604, lng: -95.3698 }, // Houston, TX (Galleria)
            "77058": { lat: 29.7604, lng: -95.3698 }, // Houston, TX (Clear Lake)
            "77059": { lat: 29.7604, lng: -95.3698 }, // Houston, TX (Clear Lake)
            "77060": { lat: 29.7604, lng: -95.3698 }, // Houston, TX (Greenspoint)
            "77061": { lat: 29.7604, lng: -95.3698 }, // Houston, TX (Southwest Houston)
            "77062": { lat: 29.7604, lng: -95.3698 }, // Houston, TX (Clear Lake)
            "77063": { lat: 29.7604, lng: -95.3698 }, // Houston, TX (Spring Branch)
            "77064": { lat: 29.7604, lng: -95.3698 }, // Houston, TX (Northwest Houston)
            "77065": { lat: 29.7604, lng: -95.3698 }, // Houston, TX (Northwest Houston)
            "77066": { lat: 29.7604, lng: -95.3698 }, // Houston, TX (Willowbrook)
            "77067": { lat: 29.7604, lng: -95.3698 }, // Houston, TX (Willowbrook)
            "77068": { lat: 29.7604, lng: -95.3698 }, // Houston, TX (Willowbrook)
            "77069": { lat: 29.7604, lng: -95.3698 }, // Houston, TX (Willowbrook)
            "77070": { lat: 29.7604, lng: -95.3698 }, // Houston, TX (Northwest Houston)
            "77071": { lat: 29.7604, lng: -95.3698 }, // Houston, TX (Southwest Houston)
            "77072": { lat: 29.7604, lng: -95.3698 }, // Houston, TX (Southwest Houston)
            "77073": { lat: 29.7604, lng: -95.3698 }, // Houston, TX (Humble)
            "77074": { lat: 29.7604, lng: -95.3698 }, // Houston, TX (Southwest Houston)
            "77075": { lat: 29.7604, lng: -95.3698 }, // Houston, TX (Southwest Houston)
            "77076": { lat: 29.7604, lng: -95.3698 }, // Houston, TX (Acres Homes)
            "77077": { lat: 29.7604, lng: -95.3698 }, // Houston, TX (Energy Corridor)
            "77078": { lat: 29.7604, lng: -95.3698 }, // Houston, TX (Acres Homes)
            "77079": { lat: 29.7604, lng: -95.3698 }, // Houston, TX (Energy Corridor)
            "77080": { lat: 29.7604, lng: -95.3698 }, // Houston, TX (Spring Branch)
            "77081": { lat: 29.7604, lng: -95.3698 }, // Houston, TX (Meyerland)
            "77082": { lat: 29.7604, lng: -95.3698 }, // Houston, TX (Westchase)
            "77083": { lat: 29.7604, lng: -95.3698 }, // Houston, TX (Alief)
            "77084": { lat: 29.7604, lng: -95.3698 }, // Houston, TX (Alief)
            "77085": { lat: 29.7604, lng: -95.3698 }, // Houston, TX (Southwest Houston)
            "77086": { lat: 29.7604, lng: -95.3698 }, // Houston, TX (Greenspoint)
            "77087": { lat: 29.7604, lng: -95.3698 }, // Houston, TX (South Houston)
            "77088": { lat: 29.7604, lng: -95.3698 }, // Houston, TX (Acres Homes)
            "77089": { lat: 29.7604, lng: -95.3698 }, // Houston, TX (South Houston)
            "77090": { lat: 29.7604, lng: -95.3698 }, // Houston, TX (Northwest Houston)
            "77091": { lat: 29.7604, lng: -95.3698 }, // Houston, TX (Acres Homes)
            "77092": { lat: 29.7604, lng: -95.3698 }, // Houston, TX (Northwest Houston)
            "77093": { lat: 29.7604, lng: -95.3698 }, // Houston, TX (Near Northside)
            "77094": { lat: 29.7604, lng: -95.3698 }, // Houston, TX (Alief)
            "77095": { lat: 29.7604, lng: -95.3698 }, // Houston, TX (Energy Corridor)
            "77096": { lat: 29.7604, lng: -95.3698 }, // Houston, TX (Gulfton)
            "77097": { lat: 29.7604, lng: -95.3698 }, // Houston, TX (Near Northside)
            "77098": { lat: 29.7604, lng: -95.3698 }, // Houston, TX (Montrose)
            "77099": { lat: 29.7604, lng: -95.3698 }, // Houston, TX (Southwest Houston)

            // Phoenix, AZ
            "85001": { lat: 33.4484, lng: -112.0740 }, // Phoenix, AZ (Downtown)
            "85002": { lat: 33.4484, lng: -112.0740 }, // Phoenix, AZ (South Phoenix)
            "85003": { lat: 33.4484, lng: -112.0740 }, // Phoenix, AZ (Downtown)
            "85004": { lat: 33.4484, lng: -112.0740 }, // Phoenix, AZ (Downtown)
            "85005": { lat: 33.4484, lng: -112.0740 }, // Phoenix, AZ (South Phoenix)
            "85006": { lat: 33.4484, lng: -112.0740 }, // Phoenix, AZ (South Phoenix)
            "85007": { lat: 33.4484, lng: -112.0740 }, // Phoenix, AZ (South Phoenix)
            "85008": { lat: 33.4484, lng: -112.0740 }, // Phoenix, AZ (Ahwatukee)
            "85009": { lat: 33.4484, lng: -112.0740 }, // Phoenix, AZ (South Phoenix)
            "85010": { lat: 33.4484, lng: -112.0740 }, // Phoenix, AZ (Ahwatukee)
            "85011": { lat: 33.4484, lng: -112.0740 }, // Phoenix, AZ (Maryvale)
            "85012": { lat: 33.4484, lng: -112.0740 }, // Phoenix, AZ (North Phoenix)
            "85013": { lat: 33.4484, lng: -112.0740 }, // Phoenix, AZ (Central Phoenix)
            "85014": { lat: 33.4484, lng: -112.0740 }, // Phoenix, AZ (Biltmore)
            "85015": { lat: 33.4484, lng: -112.0740 }, // Phoenix, AZ (Central Phoenix)
            "85016": { lat: 33.4484, lng: -112.0740 }, // Phoenix, AZ (Arcadia)
            "85017": { lat: 33.4484, lng: -112.0740 }, // Phoenix, AZ (Maryvale)
            "85018": { lat: 33.4484, lng: -112.0740 }, // Phoenix, AZ (Arcadia)
            "85019": { lat: 33.4484, lng: -112.0740 }, // Phoenix, AZ (Maryvale)
            "85020": { lat: 33.4484, lng: -112.0740 }, // Phoenix, AZ (South Phoenix)
            "85021": { lat: 33.4484, lng: -112.0740 }, // Phoenix, AZ (North Phoenix)
            "85022": { lat: 33.4484, lng: -112.0740 }, // Phoenix, AZ (North Phoenix)
            "85023": { lat: 33.4484, lng: -112.0740 }, // Phoenix, AZ (West Phoenix)
            "85024": { lat: 33.4484, lng: -112.0740 }, // Phoenix, AZ (Ahwatukee)
            "85027": { lat: 33.4484, lng: -112.0740 }, // Phoenix, AZ (Ahwatukee)
            "85028": { lat: 33.4484, lng: -112.0740 }, // Phoenix, AZ (North Phoenix)
            "85029": { lat: 33.4484, lng: -112.0740 }, // Phoenix, AZ (North Phoenix)
            "85031": { lat: 33.4484, lng: -112.0740 }, // Phoenix, AZ (Maryvale)
            "85032": { lat: 33.4484, lng: -112.0740 }, // Phoenix, AZ (Paradise Valley)
            "85033": { lat: 33.4484, lng: -112.0740 }, // Phoenix, AZ (Maryvale)
            "85034": { lat: 33.4484, lng: -112.0740 }, // Phoenix, AZ (Laveen)
            "85035": { lat: 33.4484, lng: -112.0740 }, // Phoenix, AZ (Laveen)
            "85037": { lat: 33.4484, lng: -112.0740 }, // Phoenix, AZ (Laveen)
            "85040": { lat: 33.4484, lng: -112.0740 }, // Phoenix, AZ (Ahwatukee)
            "85041": { lat: 33.4484, lng: -112.0740 }, // Phoenix, AZ (Laveen)
            "85042": { lat: 33.4484, lng: -112.0740 }, // Phoenix, AZ (Ahwatukee)
            "85043": { lat: 33.4484, lng: -112.0740 }, // Phoenix, AZ (Laveen)
            "85044": { lat: 33.4484, lng: -112.0740 }, // Phoenix, AZ (Ahwatukee)
            "85045": { lat: 33.4484, lng: -112.0740 }, // Phoenix, AZ (Ahwatukee)
            "85048": { lat: 33.4484, lng: -112.0740 }, // Phoenix, AZ (Ahwatukee)
            "85050": { lat: 33.4484, lng: -112.0740 }, // Phoenix, AZ (Ahwatukee)
            "85051": { lat: 33.4484, lng: -112.0740 }, // Phoenix, AZ (Laveen)
            "85053": { lat: 33.4484, lng: -112.0740 }, // Phoenix, AZ (West Phoenix)
            "85054": { lat: 33.4484, lng: -112.0740 }, // Phoenix, AZ (North Phoenix)
            "85083": { lat: 33.4484, lng: -112.0740 }, // Phoenix, AZ (West Phoenix)
            "85085": { lat: 33.4484, lng: -112.0740 }, // Phoenix, AZ (West Phoenix)

            // Philadelphia, PA
            "19101": { lat: 39.9526, lng: -75.1652 }, // Philadelphia, PA (Center City)
            "19102": { lat: 39.9526, lng: -75.1652 }, // Philadelphia, PA (Center City)
            "19103": { lat: 39.9526, lng: -75.1652 }, // Philadelphia, PA (Center City)
            "19104": { lat: 39.9526, lng: -75.1652 }, // Philadelphia, PA (University City)
            "19105": { lat: 39.9526, lng: -75.1652 }, // Philadelphia, PA (Society Hill)
            "19106": { lat: 39.9526, lng: -75.1652 }, // Philadelphia, PA (Old City)
            "19107": { lat: 39.9526, lng: -75.1652 }, // Philadelphia, PA (Center City)
            "19108": { lat: 39.9526, lng: -75.1652 }, // Philadelphia, PA (Center City)
            "19109": { lat: 39.9526, lng: -75.1652 }, // Philadelphia, PA (Center City)
            "19110": { lat: 39.9526, lng: -75.1652 }, // Philadelphia, PA (Hunting Park)
            "19111": { lat: 39.9526, lng: -75.1652 }, // Philadelphia, PA (Northeast)
            "19112": { lat: 39.9526, lng: -75.1652 }, // Philadelphia, PA (Northeast)
            "19113": { lat: 39.9526, lng: -75.1652 }, // Philadelphia, PA (Eastwick)
            "19114": { lat: 39.9526, lng: -75.1652 }, // Philadelphia, PA (Northeast)
            "19115": { lat: 39.9526, lng: -75.1652 }, // Philadelphia, PA (Northeast)
            "19116": { lat: 39.9526, lng: -75.1652 }, // Philadelphia, PA (Northeast)
            "19118": { lat: 39.9526, lng: -75.1652 }, // Philadelphia, PA (Mount Airy)
            "19119": { lat: 39.9526, lng: -75.1652 }, // Philadelphia, PA (Mount Airy)
            "19120": { lat: 39.9526, lng: -75.1652 }, // Philadelphia, PA (Olney)
            "19121": { lat: 39.9526, lng: -75.1652 }, // Philadelphia, PA (North Philadelphia)
            "19122": { lat: 39.9526, lng: -75.1652 }, // Philadelphia, PA (North Philadelphia)
            "19123": { lat: 39.9526, lng: -75.1652 }, // Philadelphia, PA (Northern Liberties)
            "19124": { lat: 39.9526, lng: -75.1652 }, // Philadelphia, PA (Kensington)
            "19125": { lat: 39.9526, lng: -75.1652 }, // Philadelphia, PA (Fishtown)
            "19126": { lat: 39.9526, lng: -75.1652 }, // Philadelphia, PA (North Philadelphia)
            "19127": { lat: 39.9526, lng: -75.1652 }, // Philadelphia, PA (Manayunk)
            "19128": { lat: 39.9526, lng: -75.1652 }, // Philadelphia, PA (Roxborough)
            "19129": { lat: 39.9526, lng: -75.1652 }, // Philadelphia, PA (Nicetown)
            "19130": { lat: 39.9526, lng: -75.1652 }, // Philadelphia, PA (Fairmount)
            "19131": { lat: 39.9526, lng: -75.1652 }, // Philadelphia, PA (Wynnefield)
            "19132": { lat: 39.9526, lng: -75.1652 }, // Philadelphia, PA (Strawberry Mansion)
            "19133": { lat: 39.9526, lng: -75.1652 }, // Philadelphia, PA (North Philadelphia)
            "19134": { lat: 39.9526, lng: -75.1652 }, // Philadelphia, PA (Kensington)
            "19135": { lat: 39.9526, lng: -75.1652 }, // Philadelphia, PA (Northeast)
            "19136": { lat: 39.9526, lng: -75.1652 }, // Philadelphia, PA (Northeast)
            "19137": { lat: 39.9526, lng: -75.1652 }, // Philadelphia, PA (Northeast)
            "19138": { lat: 39.9526, lng: -75.1652 }, // Philadelphia, PA (Germantown)
            "19139": { lat: 39.9526, lng: -75.1652 }, // Philadelphia, PA (West Philadelphia)
            "19140": { lat: 39.9526, lng: -75.1652 }, // Philadelphia, PA (North Philadelphia)
            "19141": { lat: 39.9526, lng: -75.1652 }, // Philadelphia, PA (Logan)
            "19142": { lat: 39.9526, lng: -75.1652 }, // Philadelphia, PA (Southwest)
            "19143": { lat: 39.9526, lng: -75.1652 }, // Philadelphia, PA (West Philadelphia)
            "19144": { lat: 39.9526, lng: -75.1652 }, // Philadelphia, PA (Germantown)
            "19145": { lat: 39.9526, lng: -75.1652 }, // Philadelphia, PA (South Philadelphia)
            "19146": { lat: 39.9526, lng: -75.1652 }, // Philadelphia, PA (South Philadelphia)
            "19147": { lat: 39.9526, lng: -75.1652 }, // Philadelphia, PA (South Philadelphia)
            "19148": { lat: 39.9526, lng: -75.1652 }, // Philadelphia, PA (South Philadelphia)
            "19149": { lat: 39.9526, lng: -75.1652 }, // Philadelphia, PA (Northeast)
            "19150": { lat: 39.9526, lng: -75.1652 }, // Philadelphia, PA (Mount Airy)
            "19151": { lat: 39.9526, lng: -75.1652 }, // Philadelphia, PA (West Philadelphia)
            "19152": { lat: 39.9526, lng: -75.1652 }, // Philadelphia, PA (Northeast)
            "19153": { lat: 39.9526, lng: -75.1652 }, // Philadelphia, PA (Southwest)
            "19154": { lat: 39.9526, lng: -75.1652 }, // Philadelphia, PA (Northeast)

            // San Antonio, TX
            "78201": { lat: 29.4241, lng: -98.4936 }, // San Antonio, TX (Downtown)
            "78202": { lat: 29.4241, lng: -98.4936 }, // San Antonio, TX (East Side)
            "78203": { lat: 29.4241, lng: -98.4936 }, // San Antonio, TX (Southtown)
            "78204": { lat: 29.4241, lng: -98.4936 }, // San Antonio, TX (South Side)
            "78205": { lat: 29.4241, lng: -98.4936 }, // San Antonio, TX (Downtown)
            "78207": { lat: 29.4241, lng: -98.4936 }, // San Antonio, TX (West Side)
            "78208": { lat: 29.4241, lng: -98.4936 }, // San Antonio, TX (East Side)
            "78209": { lat: 29.4241, lng: -98.4936 }, // San Antonio, TX (Alamo Heights)
            "78210": { lat: 29.4241, lng: -98.4936 }, // San Antonio, TX (South Side)
            "78211": { lat: 29.4241, lng: -98.4936 }, // San Antonio, TX (South Side)
            "78212": { lat: 29.4241, lng: -98.4936 }, // San Antonio, TX (Monte Vista)
            "78213": { lat: 29.4241, lng: -98.4936 }, // San Antonio, TX (North Central)
            "78214": { lat: 29.4241, lng: -98.4936 }, // San Antonio, TX (South Side)
            "78215": { lat: 29.4241, lng: -98.4936 }, // San Antonio, TX (East Side)
            "78216": { lat: 29.4241, lng: -98.4936 }, // San Antonio, TX (North Central)
            "78217": { lat: 29.4241, lng: -98.4936 }, // San Antonio, TX (Northeast)
            "78218": { lat: 29.4241, lng: -98.4936 }, // San Antonio, TX (East Side)
            "78219": { lat: 29.4241, lng: -98.4936 }, // San Antonio, TX (East Side)
            "78220": { lat: 29.4241, lng: -98.4936 }, // San Antonio, TX (East Side)
            "78221": { lat: 29.4241, lng: -98.4936 }, // San Antonio, TX (South Side)
            "78222": { lat: 29.4241, lng: -98.4936 }, // San Antonio, TX (East Side)
            "78223": { lat: 29.4241, lng: -98.4936 }, // San Antonio, TX (South Side)
            "78224": { lat: 29.4241, lng: -98.4936 }, // San Antonio, TX (South Side)
            "78225": { lat: 29.4241, lng: -98.4936 }, // San Antonio, TX (West Side)
            "78226": { lat: 29.4241, lng: -98.4936 }, // San Antonio, TX (West Side)
            "78227": { lat: 29.4241, lng: -98.4936 }, // San Antonio, TX (West Side)
            "78228": { lat: 29.4241, lng: -98.4936 }, // San Antonio, TX (Northwest)
            "78229": { lat: 29.4241, lng: -98.4936 }, // San Antonio, TX (North Side)
            "78230": { lat: 29.4241, lng: -98.4936 }, // San Antonio, TX (North Central)
            "78231": { lat: 29.4241, lng: -98.4936 }, // San Antonio, TX (North Side)
            "78232": { lat: 29.4241, lng: -98.4936 }, // San Antonio, TX (North Central)
            "78233": { lat: 29.4241, lng: -98.4936 }, // San Antonio, TX (Northeast)
            "78234": { lat: 29.4241, lng: -98.4936 }, // San Antonio, TX (Northeast)
            "78235": { lat: 29.4241, lng: -98.4936 }, // San Antonio, TX (Kelly AFB)
            "78236": { lat: 29.4241, lng: -98.4936 }, // San Antonio, TX (South Side)
            "78237": { lat: 29.4241, lng: -98.4936 }, // San Antonio, TX (West Side)
            "78238": { lat: 29.4241, lng: -98.4936 }, // San Antonio, TX (Lackland AFB)
            "78239": { lat: 29.4241, lng: -98.4936 }, // San Antonio, TX (East Side)
            "78240": { lat: 29.4241, lng: -98.4936 }, // San Antonio, TX (Northwest)
            "78242": { lat: 29.4241, lng: -98.4936 }, // San Antonio, TX (South Side)
            "78244": { lat: 29.4241, lng: -98.4936 }, // San Antonio, TX (East Side)
            "78245": { lat: 29.4241, lng: -98.4936 }, // San Antonio, TX (Southwest)
            "78247": { lat: 29.4241, lng: -98.4936 }, // San Antonio, TX (North Side)
            "78248": { lat: 29.4241, lng: -98.4936 }, // San Antonio, TX (Stone Oak)
            "78249": { lat: 29.4241, lng: -98.4936 }, // San Antonio, TX (Northwest)
            "78250": { lat: 29.4241, lng: -98.4936 }, // San Antonio, TX (Northwest)
            "78251": { lat: 29.4241, lng: -98.4936 }, // San Antonio, TX (Northwest)
            "78252": { lat: 29.4241, lng: -98.4936 }, // San Antonio, TX (North Side)
            "78253": { lat: 29.4241, lng: -98.4936 }, // San Antonio, TX (Southwest)
            "78254": { lat: 29.4241, lng: -98.4936 }, // San Antonio, TX (Northwest)
            "78255": { lat: 29.4241, lng: -98.4936 }, // San Antonio, TX (Northwest)
            "78256": { lat: 29.4241, lng: -98.4936 }, // San Antonio, TX (North Side)
            "78257": { lat: 29.4241, lng: -98.4936 }, // San Antonio, TX (Stone Oak)
            "78258": { lat: 29.4241, lng: -98.4936 }, // San Antonio, TX (Stone Oak)
            "78259": { lat: 29.4241, lng: -98.4936 }, // San Antonio, TX (Stone Oak)
            "78260": { lat: 29.4241, lng: -98.4936 }, // San Antonio, TX (Stone Oak)
            "78261": { lat: 29.4241, lng: -98.4936 }, // San Antonio, TX (Northeast)
            "78263": { lat: 29.4241, lng: -98.4936 }, // San Antonio, TX (Northeast)
            "78264": { lat: 29.4241, lng: -98.4936 }, // San Antonio, TX (Northeast)
            "78266": { lat: 29.4241, lng: -98.4936 }, // San Antonio, TX (Stone Oak)

            // San Diego, CA
            "92101": { lat: 32.7157, lng: -117.1611 }, // San Diego, CA (Downtown)
            "92102": { lat: 32.7157, lng: -117.1611 }, // San Diego, CA (Golden Hill)
            "92103": { lat: 32.7157, lng: -117.1611 }, // San Diego, CA (Balboa Park)
            "92104": { lat: 32.7157, lng: -117.1611 }, // San Diego, CA (North Park)
            "92105": { lat: 32.7157, lng: -117.1611 }, // San Diego, CA (City Heights)
            "92106": { lat: 32.7157, lng: -117.1611 }, // San Diego, CA (Point Loma)
            "92107": { lat: 32.7157, lng: -117.1611 }, // San Diego, CA (Ocean Beach)
            "92108": { lat: 32.7157, lng: -117.1611 }, // San Diego, CA (Mission Valley)
            "92109": { lat: 32.7157, lng: -117.1611 }, // San Diego, CA (Pacific Beach)
            "92110": { lat: 32.7157, lng: -117.1611 }, // San Diego, CA (Mission Bay)
            "92111": { lat: 32.7157, lng: -117.1611 }, // San Diego, CA (Clairemont)
            "92113": { lat: 32.7157, lng: -117.1611 }, // San Diego, CA (Southeast)
            "92114": { lat: 32.7157, lng: -117.1611 }, // San Diego, CA (Southeast)
            "92115": { lat: 32.7157, lng: -117.1611 }, // San Diego, CA (College Area)
            "92116": { lat: 32.7157, lng: -117.1611 }, // San Diego, CA (Normal Heights)
            "92117": { lat: 32.7157, lng: -117.1611 }, // San Diego, CA (Clairemont)
            "92118": { lat: 32.7157, lng: -117.1611 }, // San Diego, CA (Coronado)
            "92119": { lat: 32.7157, lng: -117.1611 }, // San Diego, CA (College Area)
            "92120": { lat: 32.7157, lng: -117.1611 }, // San Diego, CA (College Area)
            "92121": { lat: 32.7157, lng: -117.1611 }, // San Diego, CA (Sorrento Valley)
            "92122": { lat: 32.7157, lng: -117.1611 }, // San Diego, CA (University City)
            "92123": { lat: 32.7157, lng: -117.1611 }, // San Diego, CA (Serra Mesa)
            "92124": { lat: 32.7157, lng: -117.1611 }, // San Diego, CA (Tierrasanta)
            "92126": { lat: 32.7157, lng: -117.1611 }, // San Diego, CA (Mira Mesa)
            "92127": { lat: 32.7157, lng: -117.1611 }, // San Diego, CA (Rancho Bernardo)
            "92128": { lat: 32.7157, lng: -117.1611 }, // San Diego, CA (Rancho Bernardo)
            "92129": { lat: 32.7157, lng: -117.1611 }, // San Diego, CA (Rancho Penasquitos)
            "92130": { lat: 32.7157, lng: -117.1611 }, // San Diego, CA (Carmel Valley)
            "92131": { lat: 32.7157, lng: -117.1611 }, // San Diego, CA (Scripps Ranch)
            "92132": { lat: 32.7157, lng: -117.1611 }, // San Diego, CA (SDSU)
            "92134": { lat: 32.7157, lng: -117.1611 }, // San Diego, CA (Naval Base)
            "92135": { lat: 32.7157, lng: -117.1611 }, // San Diego, CA (Naval Base)
            "92136": { lat: 32.7157, lng: -117.1611 }, // San Diego, CA (Point Loma)
            "92139": { lat: 32.7157, lng: -117.1611 }, // San Diego, CA (Paradise Hills)
            "92140": { lat: 32.7157, lng: -117.1611 }, // San Diego, CA (Bay Terraces)
            "92145": { lat: 32.7157, lng: -117.1611 }, // San Diego, CA (Naval Base)
            "92154": { lat: 32.7157, lng: -117.1611 }, // San Diego, CA (Otay Mesa)
            "92155": { lat: 32.7157, lng: -117.1611 }, // San Diego, CA (Otay Mesa)
            "92173": { lat: 32.7157, lng: -117.1611 }, // San Diego, CA (San Ysidro)

            // Dallas, TX
            "75201": { lat: 32.7767, lng: -96.7970 }, // Dallas, TX (Downtown)
            "75202": { lat: 32.7767, lng: -96.7970 }, // Dallas, TX (Downtown)
            "75203": { lat: 32.7767, lng: -96.7970 }, // Dallas, TX (South Dallas)
            "75204": { lat: 32.7767, lng: -96.7970 }, // Dallas, TX (Deep Ellum)
            "75205": { lat: 32.7767, lng: -96.7970 }, // Dallas, TX (Highland Park)
            "75206": { lat: 32.7767, lng: -96.7970 }, // Dallas, TX (East Dallas)
            "75207": { lat: 32.7767, lng: -96.7970 }, // Dallas, TX (Oak Cliff)
            "75208": { lat: 32.7767, lng: -96.7970 }, // Dallas, TX (Oak Cliff)
            "75209": { lat: 32.7767, lng: -96.7970 }, // Dallas, TX (Turtle Creek)
            "75210": { lat: 32.7767, lng: -96.7970 }, // Dallas, TX (East Dallas)
            "75211": { lat: 32.7767, lng: -96.7970 }, // Dallas, TX (Oak Cliff)
            "75212": { lat: 32.7767, lng: -96.7970 }, // Dallas, TX (West Dallas)
            "75214": { lat: 32.7767, lng: -96.7970 }, // Dallas, TX (Lakewood)
            "75215": { lat: 32.7767, lng: -96.7970 }, // Dallas, TX (South Dallas)
            "75216": { lat: 32.7767, lng: -96.7970 }, // Dallas, TX (South Dallas)
            "75217": { lat: 32.7767, lng: -96.7970 }, // Dallas, TX (Pleasant Grove)
            "75218": { lat: 32.7767, lng: -96.7970 }, // Dallas, TX (East Dallas)
            "75219": { lat: 32.7767, lng: -96.7970 }, // Dallas, TX (Uptown)
            "75220": { lat: 32.7767, lng: -96.7970 }, // Dallas, TX (Love Field)
            "75223": { lat: 32.7767, lng: -96.7970 }, // Dallas, TX (East Dallas)
            "75224": { lat: 32.7767, lng: -96.7970 }, // Dallas, TX (East Dallas)
            "75225": { lat: 32.7767, lng: -96.7970 }, // Dallas, TX (University Park)
            "75226": { lat: 32.7767, lng: -96.7970 }, // Dallas, TX (East Dallas)
            "75227": { lat: 32.7767, lng: -96.7970 }, // Dallas, TX (Pleasant Grove)
            "75228": { lat: 32.7767, lng: -96.7970 }, // Dallas, TX (East Dallas)
            "75229": { lat: 32.7767, lng: -96.7970 }, // Dallas, TX (North Dallas)
            "75230": { lat: 32.7767, lng: -96.7970 }, // Dallas, TX (North Dallas)
            "75231": { lat: 32.7767, lng: -96.7970 }, // Dallas, TX (East Dallas)
            "75232": { lat: 32.7767, lng: -96.7970 }, // Dallas, TX (Pleasant Grove)
            "75233": { lat: 32.7767, lng: -96.7970 }, // Dallas, TX (South Dallas)
            "75234": { lat: 32.7767, lng: -96.7970 }, // Dallas, TX (North Dallas)
            "75235": { lat: 32.7767, lng: -96.7970 }, // Dallas, TX (Northwest Dallas)
            "75236": { lat: 32.7767, lng: -96.7970 }, // Dallas, TX (Southeast Dallas)
            "75237": { lat: 32.7767, lng: -96.7970 }, // Dallas, TX (Southeast Dallas)
            "75238": { lat: 32.7767, lng: -96.7970 }, // Dallas, TX (White Rock Lake)
            "75240": { lat: 32.7767, lng: -96.7970 }, // Dallas, TX (North Dallas)
            "75243": { lat: 32.7767, lng: -96.7970 }, // Dallas, TX (Northeast Dallas)
            "75244": { lat: 32.7767, lng: -96.7970 }, // Dallas, TX (North Dallas)
            "75246": { lat: 32.7767, lng: -96.7970 }, // Dallas, TX (East Dallas)
            "75247": { lat: 32.7767, lng: -96.7970 }, // Dallas, TX (West Dallas)
            "75248": { lat: 32.7767, lng: -96.7970 }, // Dallas, TX (North Dallas)
            "75249": { lat: 32.7767, lng: -96.7970 }, // Dallas, TX (North Dallas)
            "75251": { lat: 32.7767, lng: -96.7970 }, // Dallas, TX (North Dallas)
            "75252": { lat: 32.7767, lng: -96.7970 }, // Dallas, TX (North Dallas)
            "75253": { lat: 32.7767, lng: -96.7970 }, // Dallas, TX (North Dallas)
            "75254": { lat: 32.7767, lng: -96.7970 }, // Dallas, TX (North Dallas)
            "75287": { lat: 32.7767, lng: -96.7970 }, // Dallas, TX (North Dallas)

            // Jacksonville, FL
            "32099": { lat: 30.3322, lng: -81.6557 }, // Jacksonville, FL (Ponte Vedra)
            "32201": { lat: 30.3322, lng: -81.6557 }, // Jacksonville, FL (Downtown)
            "32202": { lat: 30.3322, lng: -81.6557 }, // Jacksonville, FL (Downtown)
            "32203": { lat: 30.3322, lng: -81.6557 }, // Jacksonville, FL (Springfield)
            "32204": { lat: 30.3322, lng: -81.6557 }, // Jacksonville, FL (Riverside)
            "32205": { lat: 30.3322, lng: -81.6557 }, // Jacksonville, FL (Riverside)
            "32206": { lat: 30.3322, lng: -81.6557 }, // Jacksonville, FL (Eastside)
            "32207": { lat: 30.3322, lng: -81.6557 }, // Jacksonville, FL (San Marco)
            "32208": { lat: 30.3322, lng: -81.6557 }, // Jacksonville, FL (Northwest)
            "32209": { lat: 30.3322, lng: -81.6557 }, // Jacksonville, FL (Northwest)
            "32210": { lat: 30.3322, lng: -81.6557 }, // Jacksonville, FL (Northside)
            "32211": { lat: 30.3322, lng: -81.6557 }, // Jacksonville, FL (Northside)
            "32212": { lat: 30.3322, lng: -81.6557 }, // Jacksonville, FL (Northside)
            "32216": { lat: 30.3322, lng: -81.6557 }, // Jacksonville, FL (Southside)
            "32217": { lat: 30.3322, lng: -81.6557 }, // Jacksonville, FL (Southside)
            "32218": { lat: 30.3322, lng: -81.6557 }, // Jacksonville, FL (Northside)
            "32219": { lat: 30.3322, lng: -81.6557 }, // Jacksonville, FL (Northside)
            "32220": { lat: 30.3322, lng: -81.6557 }, // Jacksonville, FL (Westside)
            "32221": { lat: 30.3322, lng: -81.6557 }, // Jacksonville, FL (Northside)
            "32222": { lat: 30.3322, lng: -81.6557 }, // Jacksonville, FL (Northside)
            "32223": { lat: 30.3322, lng: -81.6557 }, // Jacksonville, FL (Southside)
            "32224": { lat: 30.3322, lng: -81.6557 }, // Jacksonville, FL (Southside)
            "32225": { lat: 30.3322, lng: -81.6557 }, // Jacksonville, FL (Arlington)
            "32226": { lat: 30.3322, lng: -81.6557 }, // Jacksonville, FL (Southside)
            "32227": { lat: 30.3322, lng: -81.6557 }, // Jacksonville, FL (Southside)
            "32233": { lat: 30.3322, lng: -81.6557 }, // Jacksonville, FL (Eastside)
            "32234": { lat: 30.3322, lng: -81.6557 }, // Jacksonville, FL (Westside)
            "32244": { lat: 30.3322, lng: -81.6557 }, // Jacksonville, FL (Westside)
            "32246": { lat: 30.3322, lng: -81.6557 }, // Jacksonville, FL (Southside)
            "32250": { lat: 30.3322, lng: -81.6557 }, // Jacksonville, FL (Neptune Beach)
            "32254": { lat: 30.3322, lng: -81.6557 }, // Jacksonville, FL (Southside)
            "32256": { lat: 30.3322, lng: -81.6557 }, // Jacksonville, FL (Southside)
            "32257": { lat: 30.3322, lng: -81.6557 }, // Jacksonville, FL (Southside)
            "32258": { lat: 30.3322, lng: -81.6557 }, // Jacksonville, FL (Southside)
            "32259": { lat: 30.3322, lng: -81.6557 }, // Jacksonville, FL (Southside)
            "32266": { lat: 30.3322, lng: -81.6557 }, // Jacksonville, FL (Neptune Beach)
            "32277": { lat: 30.3322, lng: -81.6557 }, // Jacksonville, FL (Southside)

            // Fort Worth, TX
            "76101": { lat: 32.7555, lng: -97.3308 }, // Fort Worth, TX (Downtown)
            "76102": { lat: 32.7555, lng: -97.3308 }, // Fort Worth, TX (Downtown)
            "76103": { lat: 32.7555, lng: -97.3308 }, // Fort Worth, TX (Southside)
            "76104": { lat: 32.7555, lng: -97.3308 }, // Fort Worth, TX (Southside)
            "76105": { lat: 32.7555, lng: -97.3308 }, // Fort Worth, TX (Riverside)
            "76106": { lat: 32.7555, lng: -97.3308 }, // Fort Worth, TX (Westside)
            "76107": { lat: 32.7555, lng: -97.3308 }, // Fort Worth, TX (Cultural District)
            "76108": { lat: 32.7555, lng: -97.3308 }, // Fort Worth, TX (Westside)
            "76109": { lat: 32.7555, lng: -97.3308 }, // Fort Worth, TX (Westside)
            "76110": { lat: 32.7555, lng: -97.3308 }, // Fort Worth, TX (Eastside)
            "76111": { lat: 32.7555, lng: -97.3308 }, // Fort Worth, TX (Eastside)
            "76112": { lat: 32.7555, lng: -97.3308 }, // Fort Worth, TX (Eastside)
            "76114": { lat: 32.7555, lng: -97.3308 }, // Fort Worth, TX (Northside)
            "76115": { lat: 32.7555, lng: -97.3308 }, // Fort Worth, TX (Southside)
            "76116": { lat: 32.7555, lng: -97.3308 }, // Fort Worth, TX (Westside)
            "76117": { lat: 32.7555, lng: -97.3308 }, // Fort Worth, TX (Northside)
            "76118": { lat: 32.7555, lng: -97.3308 }, // Fort Worth, TX (Northside)
            "76119": { lat: 32.7555, lng: -97.3308 }, // Fort Worth, TX (Southside)
            "76120": { lat: 32.7555, lng: -97.3308 }, // Fort Worth, TX (Arlington Heights)
            "76123": { lat: 32.7555, lng: -97.3308 }, // Fort Worth, TX (Southwest)
            "76126": { lat: 32.7555, lng: -97.3308 }, // Fort Worth, TX (Northwest)
            "76127": { lat: 32.7555, lng: -97.3308 }, // Fort Worth, TX (Northwest)
            "76129": { lat: 32.7555, lng: -97.3308 }, // Fort Worth, TX (Southwest)
            "76131": { lat: 32.7555, lng: -97.3308 }, // Fort Worth, TX (Southwest)
            "76132": { lat: 32.7555, lng: -97.3308 }, // Fort Worth, TX (Southwest)
            "76133": { lat: 32.7555, lng: -97.3308 }, // Fort Worth, TX (Southwest)
            "76134": { lat: 32.7555, lng: -97.3308 }, // Fort Worth, TX (Southwest)
            "76135": { lat: 32.7555, lng: -97.3308 }, // Fort Worth, TX (Southwest)
            "76137": { lat: 32.7555, lng: -97.3308 }, // Fort Worth, TX (Northeast)
            "76140": { lat: 32.7555, lng: -97.3308 }, // Fort Worth, TX (Southeast)
            "76147": { lat: 32.7555, lng: -97.3308 }, // Fort Worth, TX (Northwest)
            "76148": { lat: 32.7555, lng: -97.3308 }, // Fort Worth, TX (Northwest)
            "76155": { lat: 32.7555, lng: -97.3308 }, // Fort Worth, TX (Northeast)
            "76161": { lat: 32.7555, lng: -97.3308 }, // Fort Worth, TX (Southwest)
            "76162": { lat: 32.7555, lng: -97.3308 }, // Fort Worth, TX (Southwest)
            "76163": { lat: 32.7555, lng: -97.3308 }, // Fort Worth, TX (Southwest)
            "76164": { lat: 32.7555, lng: -97.3308 }, // Fort Worth, TX (Southwest)
            "76177": { lat: 32.7555, lng: -97.3308 }, // Fort Worth, TX (Northwest)
            "76179": { lat: 32.7555, lng: -97.3308 }, // Fort Worth, TX (Northwest)
            "76180": { lat: 32.7555, lng: -97.3308 }, // Fort Worth, TX (Northeast)
            "76182": { lat: 32.7555, lng: -97.3308 }, // Fort Worth, TX (Northeast)

            // Austin, TX
            "78701": { lat: 30.2672, lng: -97.7431 }, // Austin, TX (Downtown)
            "78702": { lat: 30.2672, lng: -97.7431 }, // Austin, TX (East Austin)
            "78703": { lat: 30.2672, lng: -97.7431 }, // Austin, TX (South Austin)
            "78704": { lat: 30.2672, lng: -97.7431 }, // Austin, TX (South Austin)
            "78705": { lat: 30.2672, lng: -97.7431 }, // Austin, TX (UT Campus)
            "78712": { lat: 30.2672, lng: -97.7431 }, // Austin, TX (UT Campus)
            "78717": { lat: 30.2672, lng: -97.7431 }, // Austin, TX (Northwest)
            "78719": { lat: 30.2672, lng: -97.7431 }, // Austin, TX (Southeast)
            "78721": { lat: 30.2672, lng: -97.7431 }, // Austin, TX (East Austin)
            "78722": { lat: 30.2672, lng: -97.7431 }, // Austin, TX (East Austin)
            "78723": { lat: 30.2672, lng: -97.7431 }, // Austin, TX (East Austin)
            "78724": { lat: 30.2672, lng: -97.7431 }, // Austin, TX (East Austin)
            "78725": { lat: 30.2672, lng: -97.7431 }, // Austin, TX (East Austin)
            "78726": { lat: 30.2672, lng: -97.7431 }, // Austin, TX (Northwest)
            "78727": { lat: 30.2672, lng: -97.7431 }, // Austin, TX (Northwest)
            "78728": { lat: 30.2672, lng: -97.7431 }, // Austin, TX (Southwest)
            "78729": { lat: 30.2672, lng: -97.7431 }, // Austin, TX (Northwest)
            "78730": { lat: 30.2672, lng: -97.7431 }, // Austin, TX (West Lake Hills)
            "78731": { lat: 30.2672, lng: -97.7431 }, // Austin, TX (West Austin)
            "78732": { lat: 30.2672, lng: -97.7431 }, // Austin, TX (West Austin)
            "78733": { lat: 30.2672, lng: -97.7431 }, // Austin, TX (West Austin)
            "78734": { lat: 30.2672, lng: -97.7431 }, // Austin, TX (West Austin)
            "78735": { lat: 30.2672, lng: -97.7431 }, // Austin, TX (Southwest)
            "78736": { lat: 30.2672, lng: -97.7431 }, // Austin, TX (Southwest)
            "78737": { lat: 30.2672, lng: -97.7431 }, // Austin, TX (Southwest)
            "78738": { lat: 30.2672, lng: -97.7431 }, // Austin, TX (Southwest)
            "78739": { lat: 30.2672, lng: -97.7431 }, // Austin, TX (Southwest)
            "78741": { lat: 30.2672, lng: -97.7431 }, // Austin, TX (Southeast)
            "78742": { lat: 30.2672, lng: -97.7431 }, // Austin, TX (Southeast)
            "78744": { lat: 30.2672, lng: -97.7431 }, // Austin, TX (Southeast)
            "78745": { lat: 30.2672, lng: -97.7431 }, // Austin, TX (South Austin)
            "78746": { lat: 30.2672, lng: -97.7431 }, // Austin, TX (West Austin)
            "78747": { lat: 30.2672, lng: -97.7431 }, // Austin, TX (Southwest)
            "78748": { lat: 30.2672, lng: -97.7431 }, // Austin, TX (South Austin)
            "78749": { lat: 30.2672, lng: -97.7431 }, // Austin, TX (Southwest)
            "78750": { lat: 30.2672, lng: -97.7431 }, // Austin, TX (Northwest)
            "78751": { lat: 30.2672, lng: -97.7431 }, // Austin, TX (Central Austin)
            "78752": { lat: 30.2672, lng: -97.7431 }, // Austin, TX (East Austin)
            "78753": { lat: 30.2672, lng: -97.7431 }, // Austin, TX (North Austin)
            "78754": { lat: 30.2672, lng: -97.7431 }, // Austin, TX (East Austin)
            "78756": { lat: 30.2672, lng: -97.7431 }, // Austin, TX (Central Austin)
            "78757": { lat: 30.2672, lng: -97.7431 }, // Austin, TX (North Austin)
            "78758": { lat: 30.2672, lng: -97.7431 }, // Austin, TX (North Austin)
            "78759": { lat: 30.2672, lng: -97.7431 }, // Austin, TX (Northwest)

            // Charlotte, NC
            "28201": { lat: 35.2271, lng: -80.8431 }, // Charlotte, NC (Uptown)
            "28202": { lat: 35.2271, lng: -80.8431 }, // Charlotte, NC (Uptown)
            "28203": { lat: 35.2271, lng: -80.8431 }, // Charlotte, NC (Dilworth)
            "28204": { lat: 35.2271, lng: -80.8431 }, // Charlotte, NC (South End)
            "28205": { lat: 35.2271, lng: -80.8431 }, // Charlotte, NC (Plaza Midwood)
            "28206": { lat: 35.2271, lng: -80.8431 }, // Charlotte, NC (Elizabeth)
            "28207": { lat: 35.2271, lng: -80.8431 }, // Charlotte, NC (Myers Park)
            "28208": { lat: 35.2271, lng: -80.8431 }, // Charlotte, NC (West Charlotte)
            "28209": { lat: 35.2271, lng: -80.8431 }, // Charlotte, NC (South Park)
            "28210": { lat: 35.2271, lng: -80.8431 }, // Charlotte, NC (SouthPark)
            "28211": { lat: 35.2271, lng: -80.8431 }, // Charlotte, NC (South Charlotte)
            "28212": { lat: 35.2271, lng: -80.8431 }, // Charlotte, NC (East Charlotte)
            "28213": { lat: 35.2271, lng: -80.8431 }, // Charlotte, NC (University)
            "28214": { lat: 35.2271, lng: -80.8431 }, // Charlotte, NC (West Charlotte)
            "28215": { lat: 35.2271, lng: -80.8431 }, // Charlotte, NC (East Charlotte)
            "28216": { lat: 35.2271, lng: -80.8431 }, // Charlotte, NC (North Charlotte)
            "28217": { lat: 35.2271, lng: -80.8431 }, // Charlotte, NC (South Charlotte)
            "28226": { lat: 35.2271, lng: -80.8431 }, // Charlotte, NC (Ballantyne)
            "28227": { lat: 35.2271, lng: -80.8431 }, // Charlotte, NC (Southeast)
            "28262": { lat: 35.2271, lng: -80.8431 }, // Charlotte, NC (University)
            "28269": { lat: 35.2271, lng: -80.8431 }, // Charlotte, NC (University)
            "28270": { lat: 35.2271, lng: -80.8431 }, // Charlotte, NC (Ballantyne)
            "28273": { lat: 35.2271, lng: -80.8431 }, // Charlotte, NC (Steele Creek)
            "28277": { lat: 35.2271, lng: -80.8431 }, // Charlotte, NC (Ballantyne)
            "28278": { lat: 35.2271, lng: -80.8431 }, // Charlotte, NC (Steele Creek)
            "28280": { lat: 35.2271, lng: -80.8431 }, // Charlotte, NC (South Charlotte)
            "28282": { lat: 35.2271, lng: -80.8431 }, // Charlotte, NC (South Charlotte)

            // Columbus, OH
            "43085": { lat: 39.9612, lng: -82.9988 }, // Columbus, OH (Worthington)
            "43201": { lat: 39.9612, lng: -82.9988 }, // Columbus, OH (University District)
            "43202": { lat: 39.9612, lng: -82.9988 }, // Columbus, OH (Clintonville)
            "43203": { lat: 39.9612, lng: -82.9988 }, // Columbus, OH (South Side)
            "43204": { lat: 39.9612, lng: -82.9988 }, // Columbus, OH (Hilltop)
            "43205": { lat: 39.9612, lng: -82.9988 }, // Columbus, OH (Grandview)
            "43206": { lat: 39.9612, lng: -82.9988 }, // Columbus, OH (German Village)
            "43207": { lat: 39.9612, lng: -82.9988 }, // Columbus, OH (South Side)
            "43209": { lat: 39.9612, lng: -82.9988 }, // Columbus, OH (Bexley)
            "43210": { lat: 39.9612, lng: -82.9988 }, // Columbus, OH (OSU Campus)
            "43211": { lat: 39.9612, lng: -82.9988 }, // Columbus, OH (North Side)
            "43212": { lat: 39.9612, lng: -82.9988 }, // Columbus, OH (Clintonville)
            "43213": { lat: 39.9612, lng: -82.9988 }, // Columbus, OH (East Side)
            "43214": { lat: 39.9612, lng: -82.9988 }, // Columbus, OH (North Side)
            "43215": { lat: 39.9612, lng: -82.9988 }, // Columbus, OH (Downtown)
            "43219": { lat: 39.9612, lng: -82.9988 }, // Columbus, OH (East Side)
            "43220": { lat: 39.9612, lng: -82.9988 }, // Columbus, OH (Upper Arlington)
            "43221": { lat: 39.9612, lng: -82.9988 }, // Columbus, OH (Grandview)
            "43222": { lat: 39.9612, lng: -82.9988 }, // Columbus, OH (West Side)
            "43223": { lat: 39.9612, lng: -82.9988 }, // Columbus, OH (South Side)
            "43224": { lat: 39.9612, lng: -82.9988 }, // Columbus, OH (North Side)
            "43227": { lat: 39.9612, lng: -82.9988 }, // Columbus, OH (East Side)
            "43228": { lat: 39.9612, lng: -82.9988 }, // Columbus, OH (West Side)
            "43229": { lat: 39.9612, lng: -82.9988 }, // Columbus, OH (North Side)
            "43230": { lat: 39.9612, lng: -82.9988 }, // Columbus, OH (Westerville)
            "43231": { lat: 39.9612, lng: -82.9988 }, // Columbus, OH (Northeast)
            "43232": { lat: 39.9612, lng: -82.9988 }, // Columbus, OH (East Side)
            "43235": { lat: 39.9612, lng: -82.9988 }, // Columbus, OH (Dublin)

            // Indianapolis, IN
            "46201": { lat: 39.7684, lng: -86.1581 }, // Indianapolis, IN (Downtown)
            "46202": { lat: 39.7684, lng: -86.1581 }, // Indianapolis, IN (Near North)
            "46203": { lat: 39.7684, lng: -86.1581 }, // Indianapolis, IN (Near South)
            "46204": { lat: 39.7684, lng: -86.1581 }, // Indianapolis, IN (Near West)
            "46205": { lat: 39.7684, lng: -86.1581 }, // Indianapolis, IN (Meridian-Kessler)
            "46208": { lat: 39.7684, lng: -86.1581 }, // Indianapolis, IN (North Side)
            "46214": { lat: 39.7684, lng: -86.1581 }, // Indianapolis, IN (West Side)
            "46216": { lat: 39.7684, lng: -86.1581 }, // Indianapolis, IN (East Side)
            "46217": { lat: 39.7684, lng: -86.1581 }, // Indianapolis, IN (South Side)
            "46218": { lat: 39.7684, lng: -86.1581 }, // Indianapolis, IN (East Side)
            "46219": { lat: 39.7684, lng: -86.1581 }, // Indianapolis, IN (East Side)
            "46220": { lat: 39.7684, lng: -86.1581 }, // Indianapolis, IN (North Side)
            "46221": { lat: 39.7684, lng: -86.1581 }, // Indianapolis, IN (West Side)
            "46222": { lat: 39.7684, lng: -86.1581 }, // Indianapolis, IN (West Side)
            "46224": { lat: 39.7684, lng: -86.1581 }, // Indianapolis, IN (West Side)
            "46225": { lat: 39.7684, lng: -86.1581 }, // Indianapolis, IN (South Side)
            "46226": { lat: 39.7684, lng: -86.1581 }, // Indianapolis, IN (East Side)
            "46227": { lat: 39.7684, lng: -86.1581 }, // Indianapolis, IN (South Side)
            "46228": { lat: 39.7684, lng: -86.1581 }, // Indianapolis, IN (West Side)
            "46229": { lat: 39.7684, lng: -86.1581 }, // Indianapolis, IN (East Side)
            "46231": { lat: 39.7684, lng: -86.1581 }, // Indianapolis, IN (South Side)
            "46234": { lat: 39.7684, lng: -86.1581 }, // Indianapolis, IN (West Side)
            "46235": { lat: 39.7684, lng: -86.1581 }, // Indianapolis, IN (East Side)
            "46236": { lat: 39.7684, lng: -86.1581 }, // Indianapolis, IN (Northeast)
            "46237": { lat: 39.7684, lng: -86.1581 }, // Indianapolis, IN (Southwest)
            "46239": { lat: 39.7684, lng: -86.1581 }, // Indianapolis, IN (South Side)
            "46240": { lat: 39.7684, lng: -86.1581 }, // Indianapolis, IN (Carmel)
            "46250": { lat: 39.7684, lng: -86.1581 }, // Indianapolis, IN (North Side)
            "46254": { lat: 39.7684, lng: -86.1581 }, // Indianapolis, IN (West Side)
            "46256": { lat: 39.7684, lng: -86.1581 }, // Indianapolis, IN (North Side)
            "46260": { lat: 39.7684, lng: -86.1581 }, // Indianapolis, IN (North Side)
            "46268": { lat: 39.7684, lng: -86.1581 }, // Indianapolis, IN (North Side)
            "46278": { lat: 39.7684, lng: -86.1581 }, // Indianapolis, IN (North Side)

            // Denver, CO
            "80201": { lat: 39.7392, lng: -104.9903 }, // Denver, CO (Downtown)
            "80202": { lat: 39.7392, lng: -104.9903 }, // Denver, CO (Downtown)
            "80203": { lat: 39.7392, lng: -104.9903 }, // Denver, CO (Capitol Hill)
            "80204": { lat: 39.7392, lng: -104.9903 }, // Denver, CO (Glendale)
            "80205": { lat: 39.7392, lng: -104.9903 }, // Denver, CO (Five Points)
            "80206": { lat: 39.7392, lng: -104.9903 }, // Denver, CO (Cherry Creek)
            "80207": { lat: 39.7392, lng: -104.9903 }, // Denver, CO (Elyria-Swansea)
            "80208": { lat: 39.7392, lng: -104.9903 }, // Denver, CO (Cherry Creek)
            "80209": { lat: 39.7392, lng: -104.9903 }, // Denver, CO (Glendale)
            "80210": { lat: 39.7392, lng: -104.9903 }, // Denver, CO (Cherry Creek)
            "80211": { lat: 39.7392, lng: -104.9903 }, // Denver, CO (Berkeley)
            "80212": { lat: 39.7392, lng: -104.9903 }, // Denver, CO (Lakeside)
            "80214": { lat: 39.7392, lng: -104.9903 }, // Denver, CO (Lakewood)
            "80215": { lat: 39.7392, lng: -104.9903 }, // Denver, CO (Lakewood)
            "80216": { lat: 39.7392, lng: -104.9903 }, // Denver, CO (Elyria-Swansea)
            "80218": { lat: 39.7392, lng: -104.9903 }, // Denver, CO (Mayfair)
            "80219": { lat: 39.7392, lng: -104.9903 }, // Denver, CO (Ruby Hill)
            "80220": { lat: 39.7392, lng: -104.9903 }, // Denver, CO (Glendale)
            "80221": { lat: 39.7392, lng: -104.9903 }, // Denver, CO (Northglenn)
            "80222": { lat: 39.7392, lng: -104.9903 }, // Denver, CO (Glendale)
            "80223": { lat: 39.7392, lng: -104.9903 }, // Denver, CO (Sheridan)
            "80224": { lat: 39.7392, lng: -104.9903 }, // Denver, CO (Glendale)
            "80226": { lat: 39.7392, lng: -104.9903 }, // Denver, CO (Lakewood)
            "80227": { lat: 39.7392, lng: -104.9903 }, // Denver, CO (Lakewood)
            "80230": { lat: 39.7392, lng: -104.9903 }, // Denver, CO (Stapleton)
            "80231": { lat: 39.7392, lng: -104.9903 }, // Denver, CO (Stapleton)
            "80232": { lat: 39.7392, lng: -104.9903 }, // Denver, CO (Stapleton)
            "80233": { lat: 39.7392, lng: -104.9903 }, // Denver, CO (Northglenn)
            "80234": { lat: 39.7392, lng: -104.9903 }, // Denver, CO (Northglenn)
            "80235": { lat: 39.7392, lng: -104.9903 }, // Denver, CO (Lakewood)
            "80236": { lat: 39.7392, lng: -104.9903 }, // Denver, CO (Lakewood)
            "80237": { lat: 39.7392, lng: -104.9903 }, // Denver, CO (Montbello)
            "80238": { lat: 39.7392, lng: -104.9903 }, // Denver, CO (Stapleton)
            "80239": { lat: 39.7392, lng: -104.9903 }, // Denver, CO (Montbello)
            "80246": { lat: 39.7392, lng: -104.9903 }, // Denver, CO (Cherry Creek)
            "80247": { lat: 39.7392, lng: -104.9903 }, // Denver, CO (Stapleton)
            "80249": { lat: 39.7392, lng: -104.9903 }, // Denver, CO (Green Valley Ranch)
            "80264": { lat: 39.7392, lng: -104.9903 }, // Denver, CO (Stapleton)

            // Oklahoma City, OK
            "73101": { lat: 35.4676, lng: -97.5164 }, // Oklahoma City, OK (Downtown)
            "73102": { lat: 35.4676, lng: -97.5164 }, // Oklahoma City, OK (Downtown)
            "73103": { lat: 35.4676, lng: -97.5164 }, // Oklahoma City, OK (Northeast)
            "73104": { lat: 35.4676, lng: -97.5164 }, // Oklahoma City, OK (North)
            "73105": { lat: 35.4676, lng: -97.5164 }, // Oklahoma City, OK (Northeast)
            "73106": { lat: 35.4676, lng: -97.5164 }, // Oklahoma City, OK (Northwest)
            "73107": { lat: 35.4676, lng: -97.5164 }, // Oklahoma City, OK (Northwest)
            "73108": { lat: 35.4676, lng: -97.5164 }, // Oklahoma City, OK (Northwest)
            "73109": { lat: 35.4676, lng: -97.5164 }, // Oklahoma City, OK (South)
            "73110": { lat: 35.4676, lng: -97.5164 }, // Oklahoma City, OK (Midwest City)
            "73111": { lat: 35.4676, lng: -97.5164 }, // Oklahoma City, OK (Northeast)
            "73112": { lat: 35.4676, lng: -97.5164 }, // Oklahoma City, OK (Northwest)
            "73114": { lat: 35.4676, lng: -97.5164 }, // Oklahoma City, OK (North)
            "73115": { lat: 35.4676, lng: -97.5164 }, // Oklahoma City, OK (East)
            "73116": { lat: 35.4676, lng: -97.5164 }, // Oklahoma City, OK (Northwest)
            "73117": { lat: 35.4676, lng: -97.5164 }, // Oklahoma City, OK (North)
            "73118": { lat: 35.4676, lng: -97.5164 }, // Oklahoma City, OK (Nichols Hills)
            "73119": { lat: 35.4676, lng: -97.5164 }, // Oklahoma City, OK (South)
            "73120": { lat: 35.4676, lng: -97.5164 }, // Oklahoma City, OK (Northwest)
            "73121": { lat: 35.4676, lng: -97.5164 }, // Oklahoma City, OK (Northeast)
            "73122": { lat: 35.4676, lng: -97.5164 }, // Oklahoma City, OK (Northwest)
            "73127": { lat: 35.4676, lng: -97.5164 }, // Oklahoma City, OK (West)
            "73128": { lat: 35.4676, lng: -97.5164 }, // Oklahoma City, OK (South)
            "73129": { lat: 35.4676, lng: -97.5164 }, // Oklahoma City, OK (South)
            "73130": { lat: 35.4676, lng: -97.5164 }, // Oklahoma City, OK (Midwest City)
            "73131": { lat: 35.4676, lng: -97.5164 }, // Oklahoma City, OK (South)
            "73132": { lat: 35.4676, lng: -97.5164 }, // Oklahoma City, OK (Northwest)
            "73134": { lat: 35.4676, lng: -97.5164 }, // Oklahoma City, OK (North)
            "73135": { lat: 35.4676, lng: -97.5164 }, // Oklahoma City, OK (Southeast)
            "73139": { lat: 35.4676, lng: -97.5164 }, // Oklahoma City, OK (South)
            "73141": { lat: 35.4676, lng: -97.5164 }, // Oklahoma City, OK (Northeast)
            "73142": { lat: 35.4676, lng: -97.5164 }, // Oklahoma City, OK (Northwest)
            "73159": { lat: 35.4676, lng: -97.5164 }, // Oklahoma City, OK (South)
            "73160": { lat: 35.4676, lng: -97.5164 }, // Oklahoma City, OK (South)
            "73162": { lat: 35.4676, lng: -97.5164 }, // Oklahoma City, OK (Northwest)
            "73169": { lat: 35.4676, lng: -97.5164 }, // Oklahoma City, OK (South)
            "73170": { lat: 35.4676, lng: -97.5164 }, // Oklahoma City, OK (South)
            "73173": { lat: 35.4676, lng: -97.5164 }, // Oklahoma City, OK (South)
            "73179": { lat: 35.4676, lng: -97.5164 }, // Oklahoma City, OK (South)

            // Nashville, TN
            "37201": { lat: 36.1627, lng: -86.7816 }, // Nashville, TN (Downtown)
            "37202": { lat: 36.1627, lng: -86.7816 }, // Nashville, TN (Downtown)
            "37203": { lat: 36.1627, lng: -86.7816 }, // Nashville, TN (West End)
            "37204": { lat: 36.1627, lng: -86.7816 }, // Nashville, TN (Green Hills)
            "37205": { lat: 36.1627, lng: -86.7816 }, // Nashville, TN (Belle Meade)
            "37206": { lat: 36.1627, lng: -86.7816 }, // Nashville, TN (East Nashville)
            "37207": { lat: 36.1627, lng: -86.7816 }, // Nashville, TN (North Nashville)
            "37208": { lat: 36.1627, lng: -86.7816 }, // Nashville, TN (North Nashville)
            "37209": { lat: 36.1627, lng: -86.7816 }, // Nashville, TN (Sylvan Park)
            "37210": { lat: 36.1627, lng: -86.7816 }, // Nashville, TN (South Nashville)
            "37211": { lat: 36.1627, lng: -86.7816 }, // Nashville, TN (South Nashville)
            "37212": { lat: 36.1627, lng: -86.7816 }, // Nashville, TN (Music Row)
            "37213": { lat: 36.1627, lng: -86.7816 }, // Nashville, TN (Donelson)
            "37214": { lat: 36.1627, lng: -86.7816 }, // Nashville, TN (East Nashville)
            "37215": { lat: 36.1627, lng: -86.7816 }, // Nashville, TN (Green Hills)
            "37216": { lat: 36.1627, lng: -86.7816 }, // Nashville, TN (East Nashville)
            "37217": { lat: 36.1627, lng: -86.7816 }, // Nashville, TN (South Nashville)
            "37218": { lat: 36.1627, lng: -86.7816 }, // Nashville, TN (North Nashville)
            "37219": { lat: 36.1627, lng: -86.7816 }, // Nashville, TN (West End)
            "37220": { lat: 36.1627, lng: -86.7816 }, // Nashville, TN (Belle Meade)
            "37221": { lat: 36.1627, lng: -86.7816 }, // Nashville, TN (Bellevue)
            "37228": { lat: 36.1627, lng: -86.7816 }, // Nashville, TN (North Nashville)
            "37240": { lat: 36.1627, lng: -86.7816 }, // Nashville, TN (Vanderbilt)

            // Washington, DC
            "20001": { lat: 38.9072, lng: -77.0369 }, // Washington, DC (Northeast)
            "20002": { lat: 38.9072, lng: -77.0369 }, // Washington, DC (Northeast)
            "20003": { lat: 38.9072, lng: -77.0369 }, // Washington, DC (Southeast)
            "20004": { lat: 38.9072, lng: -77.0369 }, // Washington, DC (Downtown)
            "20005": { lat: 38.9072, lng: -77.0369 }, // Washington, DC (Downtown)
            "20006": { lat: 38.9072, lng: -77.0369 }, // Washington, DC (Downtown)
            "20007": { lat: 38.9072, lng: -77.0369 }, // Washington, DC (Georgetown)
            "20008": { lat: 38.9072, lng: -77.0369 }, // Washington, DC (Northwest)
            "20009": { lat: 38.9072, lng: -77.0369 }, // Washington, DC (Dupont Circle)
            "20010": { lat: 38.9072, lng: -77.0369 }, // Washington, DC (Columbia Heights)
            "20011": { lat: 38.9072, lng: -77.0369 }, // Washington, DC (Northwest)
            "20012": { lat: 38.9072, lng: -77.0369 }, // Washington, DC (Northeast)
            "20015": { lat: 38.9072, lng: -77.0369 }, // Washington, DC (Northwest)
            "20016": { lat: 38.9072, lng: -77.0369 }, // Washington, DC (Northwest)
            "20017": { lat: 38.9072, lng: -77.0369 }, // Washington, DC (Northeast)
            "20018": { lat: 38.9072, lng: -77.0369 }, // Washington, DC (Northeast)
            "20019": { lat: 38.9072, lng: -77.0369 }, // Washington, DC (Southeast)
            "20020": { lat: 38.9072, lng: -77.0369 }, // Washington, DC (Southeast)
            "20024": { lat: 38.9072, lng: -77.0369 }, // Washington, DC (Southwest)
            "20032": { lat: 38.9072, lng: -77.0369 }, // Washington, DC (Southeast)
            "20036": { lat: 38.9072, lng: -77.0369 }, // Washington, DC (Dupont Circle)
            "20037": { lat: 38.9072, lng: -77.0369 }, // Washington, DC (Foggy Bottom)
            "20052": { lat: 38.9072, lng: -77.0369 }, // Washington, DC (GWU)
            "20057": { lat: 38.9072, lng: -77.0369 }, // Washington, DC (Georgetown University)
            "20064": { lat: 38.9072, lng: -77.0369 }, // Washington, DC (Southwest)

            // El Paso, TX
            "79901": { lat: 31.7619, lng: -106.4850 }, // El Paso, TX (Downtown)
            "79902": { lat: 31.7619, lng: -106.4850 }, // El Paso, TX (South Central)
            "79903": { lat: 31.7619, lng: -106.4850 }, // El Paso, TX (Central)
            "79904": { lat: 31.7619, lng: -106.4850 }, // El Paso, TX (East)
            "79905": { lat: 31.7619, lng: -106.4850 }, // El Paso, TX (Central)
            "79906": { lat: 31.7619, lng: -106.4850 }, // El Paso, TX (East)
            "79907": { lat: 31.7619, lng: -106.4850 }, // El Paso, TX (Northeast)
            "79908": { lat: 31.7619, lng: -106.4850 }, // El Paso, TX (Northeast)
            "79911": { lat: 31.7619, lng: -106.4850 }, // El Paso, TX (East)
            "79912": { lat: 31.7619, lng: -106.4850 }, // El Paso, TX (West)
            "79915": { lat: 31.7619, lng: -106.4850 }, // El Paso, TX (East)
            "79922": { lat: 31.7619, lng: -106.4850 }, // El Paso, TX (Northeast)
            "79924": { lat: 31.7619, lng: -106.4850 }, // El Paso, TX (Northeast)
            "79925": { lat: 31.7619, lng: -106.4850 }, // El Paso, TX (Northeast)
            "79927": { lat: 31.7619, lng: -106.4850 }, // El Paso, TX (West)
            "79928": { lat: 31.7619, lng: -106.4850 }, // El Paso, TX (West)
            "79930": { lat: 31.7619, lng: -106.4850 }, // El Paso, TX (West)
            "79932": { lat: 31.7619, lng: -106.4850 }, // El Paso, TX (West)
            "79934": { lat: 31.7619, lng: -106.4850 }, // El Paso, TX (West)
            "79935": { lat: 31.7619, lng: -106.4850 }, // El Paso, TX (West)
            "79936": { lat: 31.7619, lng: -106.4850 }, // El Paso, TX (West)
            "79938": { lat: 31.7619, lng: -106.4850 }, // El Paso, TX (West)

            // Boston, MA
            "02101": { lat: 42.3601, lng: -71.0589 }, // Boston, MA (Downtown)
            "02102": { lat: 42.3601, lng: -71.0589 }, // Boston, MA (Downtown)
            "02103": { lat: 42.3601, lng: -71.0589 }, // Boston, MA (Downtown)
            "02104": { lat: 42.3601, lng: -71.0589 }, // Boston, MA (Downtown)
            "02105": { lat: 42.3601, lng: -71.0589 }, // Boston, MA (Downtown)
            "02106": { lat: 42.3601, lng: -71.0589 }, // Boston, MA (Downtown)
            "02107": { lat: 42.3601, lng: -71.0589 }, // Boston, MA (Downtown)
            "02108": { lat: 42.3601, lng: -71.0589 }, // Boston, MA (Beacon Hill)
            "02109": { lat: 42.3601, lng: -71.0589 }, // Boston, MA (North End)
            "02110": { lat: 42.3601, lng: -71.0589 }, // Boston, MA (Downtown)
            "02111": { lat: 42.3601, lng: -71.0589 }, // Boston, MA (Chinatown)
            "02112": { lat: 42.3601, lng: -71.0589 }, // Boston, MA (South End)
            "02113": { lat: 42.3601, lng: -71.0589 }, // Boston, MA (North End)
            "02114": { lat: 42.3601, lng: -71.0589 }, // Boston, MA (Beacon Hill)
            "02115": { lat: 42.3601, lng: -71.0589 }, // Boston, MA (Back Bay)
            "02116": { lat: 42.3601, lng: -71.0589 }, // Boston, MA (Back Bay)
            "02117": { lat: 42.3601, lng: -71.0589 }, // Boston, MA (Back Bay)
            "02118": { lat: 42.3601, lng: -71.0589 }, // Boston, MA (South End)
            "02119": { lat: 42.3601, lng: -71.0589 }, // Boston, MA (Roxbury)
            "02120": { lat: 42.3601, lng: -71.0589 }, // Boston, MA (Mission Hill)
            "02121": { lat: 42.3601, lng: -71.0589 }, // Boston, MA (Dorchester)
            "02122": { lat: 42.3601, lng: -71.0589 }, // Boston, MA (Dorchester)
            "02123": { lat: 42.3601, lng: -71.0589 }, // Boston, MA (Dorchester)
            "02124": { lat: 42.3601, lng: -71.0589 }, // Boston, MA (Dorchester)
            "02125": { lat: 42.3601, lng: -71.0589 }, // Boston, MA (Dorchester)
            "02126": { lat: 42.3601, lng: -71.0589 }, // Boston, MA (Mattapan)
            "02127": { lat: 42.3601, lng: -71.0589 }, // Boston, MA (South Boston)
            "02128": { lat: 42.3601, lng: -71.0589 }, // Boston, MA (East Boston)
            "02129": { lat: 42.3601, lng: -71.0589 }, // Boston, MA (Charlestown)
            "02130": { lat: 42.3601, lng: -71.0589 }, // Boston, MA (Jamaica Plain)
            "02131": { lat: 42.3601, lng: -71.0589 }, // Boston, MA (Roslindale)
            "02132": { lat: 42.3601, lng: -71.0589 }, // Boston, MA (West Roxbury)
            "02133": { lat: 42.3601, lng: -71.0589 }, // Boston, MA (Downtown)
            "02134": { lat: 42.3601, lng: -71.0589 }, // Boston, MA (Allston)
            "02135": { lat: 42.3601, lng: -71.0589 }, // Boston, MA (Brighton)
            "02136": { lat: 42.3601, lng: -71.0589 }, // Boston, MA (Hyde Park)
            "02137": { lat: 42.3601, lng: -71.0589 }, // Boston, MA (Readville)
            "02163": { lat: 42.3601, lng: -71.0589 }, // Boston, MA (Back Bay)
            "02199": { lat: 42.3601, lng: -71.0589 }, // Boston, MA (Back Bay)
            "02201": { lat: 42.3601, lng: -71.0589 }, // Boston, MA (Downtown)
            "02203": { lat: 42.3601, lng: -71.0589 }, // Boston, MA (Downtown)
            "02204": { lat: 42.3601, lng: -71.0589 }, // Boston, MA (South Boston)
            "02205": { lat: 42.3601, lng: -71.0589 }, // Boston, MA (Back Bay)
            "02206": { lat: 42.3601, lng: -71.0589 }, // Boston, MA (South Boston)
            "02210": { lat: 42.3601, lng: -71.0589 }, // Boston, MA (South Boston)
            "02211": { lat: 42.3601, lng: -71.0589 }, // Boston, MA (South Boston)
            "02212": { lat: 42.3601, lng: -71.0589 }, // Boston, MA (South Boston)
            "02215": { lat: 42.3601, lng: -71.0589 }, // Boston, MA (Fenway)
            "02467": { lat: 42.3601, lng: -71.0589 }, // Boston, MA (Chestnut Hill)

            // Detroit, MI
            "48201": { lat: 42.3314, lng: -83.0458 }, // Detroit, MI (Downtown)
            "48202": { lat: 42.3314, lng: -83.0458 }, // Detroit, MI (Downtown)
            "48203": { lat: 42.3314, lng: -83.0458 }, // Detroit, MI (New Center)
            "48204": { lat: 42.3314, lng: -83.0458 }, // Detroit, MI (Southwest)
            "48205": { lat: 42.3314, lng: -83.0458 }, // Detroit, MI (East Side)
            "48206": { lat: 42.3314, lng: -83.0458 }, // Detroit, MI (East Side)
            "48207": { lat: 42.3314, lng: -83.0458 }, // Detroit, MI (East Side)
            "48208": { lat: 42.3314, lng: -83.0458 }, // Detroit, MI (Southwest)
            "48209": { lat: 42.3314, lng: -83.0458 }, // Detroit, MI (Southwest)
            "48210": { lat: 42.3314, lng: -83.0458 }, // Detroit, MI (Southwest)
            "48211": { lat: 42.3314, lng: -83.0458 }, // Detroit, MI (West Side)
            "48212": { lat: 42.3314, lng: -83.0458 }, // Detroit, MI (Highland Park)
            "48213": { lat: 42.3314, lng: -83.0458 }, // Detroit, MI (East Side)
            "48214": { lat: 42.3314, lng: -83.0458 }, // Detroit, MI (East Side)
            "48215": { lat: 42.3314, lng: -83.0458 }, // Detroit, MI (Southwest)
            "48216": { lat: 42.3314, lng: -83.0458 }, // Detroit, MI (Southwest)
            "48217": { lat: 42.3314, lng: -83.0458 }, // Detroit, MI (Southwest)
            "48218": { lat: 42.3314, lng: -83.0458 }, // Detroit, MI (Northwest)
            "48219": { lat: 42.3314, lng: -83.0458 }, // Detroit, MI (Northwest)
            "48220": { lat: 42.3314, lng: -83.0458 }, // Detroit, MI (Northwest)
            "48221": { lat: 42.3314, lng: -83.0458 }, // Detroit, MI (Northwest)
            "48222": { lat: 42.3314, lng: -83.0458 }, // Detroit, MI (East Side)
            "48223": { lat: 42.3314, lng: -83.0458 }, // Detroit, MI (Southwest)
            "48224": { lat: 42.3314, lng: -83.0458 }, // Detroit, MI (East Side)
            "48225": { lat: 42.3314, lng: -83.0458 }, // Detroit, MI (Southwest)
            "48226": { lat: 42.3314, lng: -83.0458 }, // Detroit, MI (Downtown)
            "48227": { lat: 42.3314, lng: -83.0458 }, // Detroit, MI (Southwest)
            "48228": { lat: 42.3314, lng: -83.0458 }, // Detroit, MI (West Side)
            "48229": { lat: 42.3314, lng: -83.0458 }, // Detroit, MI (East Side)
            "48230": { lat: 42.3314, lng: -83.0458 }, // Detroit, MI (East Side)
            "48231": { lat: 42.3314, lng: -83.0458 }, // Detroit, MI (East Side)
            "48232": { lat: 42.3314, lng: -83.0458 }, // Detroit, MI (North End)
            "48233": { lat: 42.3314, lng: -83.0458 }, // Detroit, MI (Downtown)
            "48234": { lat: 42.3314, lng: -83.0458 }, // Detroit, MI (East Side)
            "48235": { lat: 42.3314, lng: -83.0458 }, // Detroit, MI (East Side)
            "48236": { lat: 42.3314, lng: -83.0458 }, // Detroit, MI (East Side)
            "48237": { lat: 42.3314, lng: -83.0458 }, // Detroit, MI (East Side)
            "48238": { lat: 42.3314, lng: -83.0458 }, // Detroit, MI (East Side)
            "48239": { lat: 42.3314, lng: -83.0458 }, // Detroit, MI (Redford)
            "48240": { lat: 42.3314, lng: -83.0458 }, // Detroit, MI (East Side)
            "48241": { lat: 42.3314, lng: -83.0458 }, // Detroit, MI (East Side)
            "48242": { lat: 42.3314, lng: -83.0458 }, // Detroit, MI (River Rouge)
            "48243": { lat: 42.3314, lng: -83.0458 }, // Detroit, MI (East Side)
            "48244": { lat: 42.3314, lng: -83.0458 }, // Detroit, MI (East Side)

            // Louisville, KY
            "40201": { lat: 38.2527, lng: -85.7585 }, // Louisville, KY (Downtown)
            "40202": { lat: 38.2527, lng: -85.7585 }, // Louisville, KY (Downtown)
            "40203": { lat: 38.2527, lng: -85.7585 }, // Louisville, KY (Old Louisville)
            "40204": { lat: 38.2527, lng: -85.7585 }, // Louisville, KY (Old Louisville)
            "40205": { lat: 38.2527, lng: -85.7585 }, // Louisville, KY (Highlands)
            "40206": { lat: 38.2527, lng: -85.7585 }, // Louisville, KY (Highlands)
            "40207": { lat: 38.2527, lng: -85.7585 }, // Louisville, KY (St. Matthews)
            "40208": { lat: 38.2527, lng: -85.7585 }, // Louisville, KY (South End)
            "40209": { lat: 38.2527, lng: -85.7585 }, // Louisville, KY (South End)
            "40210": { lat: 38.2527, lng: -85.7585 }, // Louisville, KY (West End)
            "40211": { lat: 38.2527, lng: -85.7585 }, // Louisville, KY (West End)
            "40212": { lat: 38.2527, lng: -85.7585 }, // Louisville, KY (West End)
            "40213": { lat: 38.2527, lng: -85.7585 }, // Louisville, KY (South End)
            "40214": { lat: 38.2527, lng: -85.7585 }, // Louisville, KY (South End)
            "40215": { lat: 38.2527, lng: -85.7585 }, // Louisville, KY (South End)
            "40216": { lat: 38.2527, lng: -85.7585 }, // Louisville, KY (West End)
            "40217": { lat: 38.2527, lng: -85.7585 }, // Louisville, KY (South End)
            "40218": { lat: 38.2527, lng: -85.7585 }, // Louisville, KY (East End)
            "40219": { lat: 38.2527, lng: -85.7585 }, // Louisville, KY (South End)
            "40220": { lat: 38.2527, lng: -85.7585 }, // Louisville, KY (East End)
            "40221": { lat: 38.2527, lng: -85.7585 }, // Louisville, KY (East End)
            "40222": { lat: 38.2527, lng: -85.7585 }, // Louisville, KY (East End)
            "40223": { lat: 38.2527, lng: -85.7585 }, // Louisville, KY (East End)
            "40224": { lat: 38.2527, lng: -85.7585 }, // Louisville, KY (East End)
            "40225": { lat: 38.2527, lng: -85.7585 }, // Louisville, KY (East End)
            "40228": { lat: 38.2527, lng: -85.7585 }, // Louisville, KY (South End)
            "40229": { lat: 38.2527, lng: -85.7585 }, // Louisville, KY (East End)
            "40231": { lat: 38.2527, lng: -85.7585 }, // Louisville, KY (East End)
            "40232": { lat: 38.2527, lng: -85.7585 }, // Louisville, KY (East End)
            "40233": { lat: 38.2527, lng: -85.7585 }, // Louisville, KY (East End)
            "40241": { lat: 38.2527, lng: -85.7585 }, // Louisville, KY (East End)
            "40242": { lat: 38.2527, lng: -85.7585 }, // Louisville, KY (East End)
            "40243": { lat: 38.2527, lng: -85.7585 }, // Louisville, KY (East End)
            "40245": { lat: 38.2527, lng: -85.7585 }, // Louisville, KY (East End)
            "40258": { lat: 38.2527, lng: -85.7585 }, // Louisville, KY (South End)
            "40259": { lat: 38.2527, lng: -85.7585 }, // Louisville, KY (South End)
            "40261": { lat: 38.2527, lng: -85.7585 }, // Louisville, KY (Southwest)
            "40266": { lat: 38.2527, lng: -85.7585 }, // Louisville, KY (Southwest)
            "40268": { lat: 38.2527, lng: -85.7585 }, // Louisville, KY (Southwest)
            "40269": { lat: 38.2527, lng: -85.7585 }, // Louisville, KY (Southwest)
            "40270": { lat: 38.2527, lng: -85.7585 }, // Louisville, KY (Southwest)
            "40272": { lat: 38.2527, lng: -85.7585 }, // Louisville, KY (Southwest)
            "40280": { lat: 38.2527, lng: -85.7585 }, // Louisville, KY (Southwest)
            "40281": { lat: 38.2527, lng: -85.7585 }, // Louisville, KY (Southwest)
            "40282": { lat: 38.2527, lng: -85.7585 }, // Louisville, KY (Southwest)
            "40283": { lat: 38.2527, lng: -85.7585 }, // Louisville, KY (Southwest)
            "40285": { lat: 38.2527, lng: -85.7585 }, // Louisville, KY (Southwest)
            "40287": { lat: 38.2527, lng: -85.7585 }, // Louisville, KY (Southwest)
            "40289": { lat: 38.2527, lng: -85.7585 }, // Louisville, KY (East End)
            "40290": { lat: 38.2527, lng: -85.7585 }, // Louisville, KY (East End)
            "40291": { lat: 38.2527, lng: -85.7585 }, // Louisville, KY (East End)
            "40292": { lat: 38.2527, lng: -85.7585 }, // Louisville, KY (East End)
            "40293": { lat: 38.2527, lng: -85.7585 }, // Louisville, KY (East End)
            "40294": { lat: 38.2527, lng: -85.7585 }, // Louisville, KY (Airport)
            "40295": { lat: 38.2527, lng: -85.7585 }, // Louisville, KY (East End)
            "40296": { lat: 38.2527, lng: -85.7585 }, // Louisville, KY (Southwest)
            "40297": { lat: 38.2527, lng: -85.7585 }, // Louisville, KY (Southwest)
            "40298": { lat: 38.2527, lng: -85.7585 }, // Louisville, KY (Southwest)
            "40299": { lat: 38.2527, lng: -85.7585 }, // Louisville, KY (East End)

            // Portland, OR
            "97201": { lat: 45.5152, lng: -122.6784 }, // Portland, OR (Downtown)
            "97202": { lat: 45.5152, lng: -122.6784 }, // Portland, OR (Southeast)
            "97203": { lat: 45.5152, lng: -122.6784 }, // Portland, OR (Northwest)
            "97204": { lat: 45.5152, lng: -122.6784 }, // Portland, OR (Downtown)
            "97205": { lat: 45.5152, lng: -122.6784 }, // Portland, OR (Northwest)
            "97206": { lat: 45.5152, lng: -122.6784 }, // Portland, OR (Southeast)
            "97207": { lat: 45.5152, lng: -122.6784 }, // Portland, OR (Northwest)
            "97208": { lat: 45.5152, lng: -122.6784 }, // Portland, OR (Downtown)
            "97209": { lat: 45.5152, lng: -122.6784 }, // Portland, OR (Northwest)
            "97210": { lat: 45.5152, lng: -122.6784 }, // Portland, OR (Northwest)
            "97211": { lat: 45.5152, lng: -122.6784 }, // Portland, OR (North)
            "97212": { lat: 45.5152, lng: -122.6784 }, // Portland, OR (Northeast)
            "97213": { lat: 45.5152, lng: -122.6784 }, // Portland, OR (Northeast)
            "97214": { lat: 45.5152, lng: -122.6784 }, // Portland, OR (Southeast)
            "97215": { lat: 45.5152, lng: -122.6784 }, // Portland, OR (Southeast)
            "97216": { lat: 45.5152, lng: -122.6784 }, // Portland, OR (Northeast)
            "97217": { lat: 45.5152, lng: -122.6784 }, // Portland, OR (North)
            "97218": { lat: 45.5152, lng: -122.6784 }, // Portland, OR (Northeast)
            "97219": { lat: 45.5152, lng: -122.6784 }, // Portland, OR (Southwest)
            "97220": { lat: 45.5152, lng: -122.6784 }, // Portland, OR (Northeast)
            "97221": { lat: 45.5152, lng: -122.6784 }, // Portland, OR (Northwest)
            "97222": { lat: 45.5152, lng: -122.6784 }, // Portland, OR (Southeast)
            "97223": { lat: 45.5152, lng: -122.6784 }, // Portland, OR (Southwest)
            "97224": { lat: 45.5152, lng: -122.6784 }, // Portland, OR (Southwest)
            "97225": { lat: 45.5152, lng: -122.6784 }, // Portland, OR (Northwest)
            "97227": { lat: 45.5152, lng: -122.6784 }, // Portland, OR (North)
            "97228": { lat: 45.5152, lng: -122.6784 }, // Portland, OR (Northeast)
            "97229": { lat: 45.5152, lng: -122.6784 }, // Portland, OR (Northwest)
            "97230": { lat: 45.5152, lng: -122.6784 }, // Portland, OR (Northeast)
            "97231": { lat: 45.5152, lng: -122.6784 }, // Portland, OR (Northwest)
            "97232": { lat: 45.5152, lng: -122.6784 }, // Portland, OR (Southeast)
            "97233": { lat: 45.5152, lng: -122.6784 }, // Portland, OR (East)
            "97236": { lat: 45.5152, lng: -122.6784 }, // Portland, OR (East)
            "97238": { lat: 45.5152, lng: -122.6784 }, // Portland, OR (Downtown)
            "97239": { lat: 45.5152, lng: -122.6784 }, // Portland, OR (Southwest)
            "97240": { lat: 45.5152, lng: -122.6784 }, // Portland, OR (Northeast)
            "97242": { lat: 45.5152, lng: -122.6784 }, // Portland, OR (Southeast)
            "97266": { lat: 45.5152, lng: -122.6784 }, // Portland, OR (Southeast)
            "97267": { lat: 45.5152, lng: -122.6784 }, // Portland, OR (Southeast)
            "97268": { lat: 45.5152, lng: -122.6784 }, // Portland, OR (Southeast)
            "97269": { lat: 45.5152, lng: -122.6784 }, // Portland, OR (East)
            "97280": { lat: 45.5152, lng: -122.6784 }, // Portland, OR (Downtown)
            "97281": { lat: 45.5152, lng: -122.6784 }, // Portland, OR (Southwest)
            "97282": { lat: 45.5152, lng: -122.6784 }, // Portland, OR (Southeast)
            "97283": { lat: 45.5152, lng: -122.6784 }, // Portland, OR (East)
            "97286": { lat: 45.5152, lng: -122.6784 }, // Portland, OR (Southeast)
            "97290": { lat: 45.5152, lng: -122.6784 }, // Portland, OR (Downtown)
            "97291": { lat: 45.5152, lng: -122.6784 }, // Portland, OR (Northwest)
            "97292": { lat: 45.5152, lng: -122.6784 }, // Portland, OR (Southeast)
            "97293": { lat: 45.5152, lng: -122.6784 }, // Portland, OR (North)
            "97294": { lat: 45.5152, lng: -122.6784 }, // Portland, OR (Northwest)
            "97296": { lat: 45.5152, lng: -122.6784 }, // Portland, OR (Southwest)
            "97298": { lat: 45.5152, lng: -122.6784 }, // Portland, OR (Downtown)
            "97299": { lat: 45.5152, lng: -122.6784 }, // Portland, OR (Northwest)

            // Memphis, TN
            "38101": { lat: 35.1495, lng: -90.0490 }, // Memphis, TN (Downtown)
            "38103": { lat: 35.1495, lng: -90.0490 }, // Memphis, TN (Downtown)
            "38104": { lat: 35.1495, lng: -90.0490 }, // Memphis, TN (Midtown)
            "38105": { lat: 35.1495, lng: -90.0490 }, // Memphis, TN (Midtown)
            "38106": { lat: 35.1495, lng: -90.0490 }, // Memphis, TN (South Memphis)
            "38107": { lat: 35.1495, lng: -90.0490 }, // Memphis, TN (North Memphis)
            "38108": { lat: 35.1495, lng: -90.0490 }, // Memphis, TN (South Memphis)
            "38109": { lat: 35.1495, lng: -90.0490 }, // Memphis, TN (Whitehaven)
            "38111": { lat: 35.1495, lng: -90.0490 }, // Memphis, TN (East Memphis)
            "38112": { lat: 35.1495, lng: -90.0490 }, // Memphis, TN (East Memphis)
            "38114": { lat: 35.1495, lng: -90.0490 }, // Memphis, TN (East Memphis)
            "38115": { lat: 35.1495, lng: -90.0490 }, // Memphis, TN (East Memphis)
            "38116": { lat: 35.1495, lng: -90.0490 }, // Memphis, TN (East Memphis)
            "38117": { lat: 35.1495, lng: -90.0490 }, // Memphis, TN (East Memphis)
            "38118": { lat: 35.1495, lng: -90.0490 }, // Memphis, TN (East Memphis)
            "38119": { lat: 35.1495, lng: -90.0490 }, // Memphis, TN (East Memphis)
            "38120": { lat: 35.1495, lng: -90.0490 }, // Memphis, TN (East Memphis)
            "38122": { lat: 35.1495, lng: -90.0490 }, // Memphis, TN (East Memphis)
            "38125": { lat: 35.1495, lng: -90.0490 }, // Memphis, TN (Southeast)
            "38126": { lat: 35.1495, lng: -90.0490 }, // Memphis, TN (Southwest)
            "38127": { lat: 35.1495, lng: -90.0490 }, // Memphis, TN (North Memphis)
            "38128": { lat: 35.1495, lng: -90.0490 }, // Memphis, TN (East Memphis)
            "38133": { lat: 35.1495, lng: -90.0490 }, // Memphis, TN (East Memphis)
            "38134": { lat: 35.1495, lng: -90.0490 }, // Memphis, TN (North Memphis)
            "38135": { lat: 35.1495, lng: -90.0490 }, // Memphis, TN (East Memphis)
            "38138": { lat: 35.1495, lng: -90.0490 }, // Memphis, TN (Germantown)
            "38139": { lat: 35.1495, lng: -90.0490 }, // Memphis, TN (Germantown)
            "38141": { lat: 35.1495, lng: -90.0490 }, // Memphis, TN (East Memphis)
            "38152": { lat: 35.1495, lng: -90.0490 }, // Memphis, TN (Cordova)
            "38157": { lat: 35.1495, lng: -90.0490 }, // Memphis, TN (Germantown)
            "38159": { lat: 35.1495, lng: -90.0490 }, // Memphis, TN (East Memphis)
            "38161": { lat: 35.1495, lng: -90.0490 }, // Memphis, TN (East Memphis)
            "38163": { lat: 35.1495, lng: -90.0490 }, // Memphis, TN (East Memphis)
            "38166": { lat: 35.1495, lng: -90.0490 }, // Memphis, TN (East Memphis)
            "38167": { lat: 35.1495, lng: -90.0490 }, // Memphis, TN (East Memphis)
            "38168": { lat: 35.1495, lng: -90.0490 }, // Memphis, TN (East Memphis)
            "38173": { lat: 35.1495, lng: -90.0490 }, // Memphis, TN (East Memphis)
            "38174": { lat: 35.1495, lng: -90.0490 }, // Memphis, TN (East Memphis)
            "38175": { lat: 35.1495, lng: -90.0490 }, // Memphis, TN (East Memphis)
            "38177": { lat: 35.1495, lng: -90.0490 }, // Memphis, TN (East Memphis)
            "38181": { lat: 35.1495, lng: -90.0490 }, // Memphis, TN (East Memphis)
            "38182": { lat: 35.1495, lng: -90.0490 }, // Memphis, TN (East Memphis)
            "38183": { lat: 35.1495, lng: -90.0490 }, // Memphis, TN (East Memphis)
            "38184": { lat: 35.1495, lng: -90.0490 }, // Memphis, TN (East Memphis)
            "38186": { lat: 35.1495, lng: -90.0490 }, // Memphis, TN (East Memphis)
            "38187": { lat: 35.1495, lng: -90.0490 }, // Memphis, TN (East Memphis)
            "38188": { lat: 35.1495, lng: -90.0490 }, // Memphis, TN (East Memphis)
            "38190": { lat: 35.1495, lng: -90.0490 }, // Memphis, TN (East Memphis)
            "38193": { lat: 35.1495, lng: -90.0490 }, // Memphis, TN (East Memphis)
            "38194": { lat: 35.1495, lng: -90.0490 }, // Memphis, TN (East Memphis)
            "38197": { lat: 35.1495, lng: -90.0490 }, // Memphis, TN (East Memphis)

            // Baltimore, MD
            "21201": { lat: 39.2904, lng: -76.6122 }, // Baltimore, MD (Downtown)
            "21202": { lat: 39.2904, lng: -76.6122 }, // Baltimore, MD (Downtown)
            "21203": { lat: 39.2904, lng: -76.6122 }, // Baltimore, MD (Southwest)
            "21204": { lat: 39.2904, lng: -76.6122 }, // Baltimore, MD (Towson)
            "21205": { lat: 39.2904, lng: -76.6122 }, // Baltimore, MD (East Baltimore)
            "21206": { lat: 39.2904, lng: -76.6122 }, // Baltimore, MD (Northeast)
            "21207": { lat: 39.2904, lng: -76.6122 }, // Baltimore, MD (Gwynn Oak)
            "21208": { lat: 39.2904, lng: -76.6122 }, // Baltimore, MD (Pikesville)
            "21209": { lat: 39.2904, lng: -76.6122 }, // Baltimore, MD (Mt Washington)
            "21210": { lat: 39.2904, lng: -76.6122 }, // Baltimore, MD (Roland Park)
            "21211": { lat: 39.2904, lng: -76.6122 }, // Baltimore, MD (Hampden)
            "21212": { lat: 39.2904, lng: -76.6122 }, // Baltimore, MD (Govans)
            "21213": { lat: 39.2904, lng: -76.6122 }, // Baltimore, MD (Clifton)
            "21214": { lat: 39.2904, lng: -76.6122 }, // Baltimore, MD (Hamilton)
            "21215": { lat: 39.2904, lng: -76.6122 }, // Baltimore, MD (West Baltimore)
            "21216": { lat: 39.2904, lng: -76.6122 }, // Baltimore, MD (West Baltimore)
            "21217": { lat: 39.2904, lng: -76.6122 }, // Baltimore, MD (Druid Heights)
            "21218": { lat: 39.2904, lng: -76.6122 }, // Baltimore, MD (Charles Village)
            "21219": { lat: 39.2904, lng: -76.6122 }, // Baltimore, MD (Sparrows Point)
            "21220": { lat: 39.2904, lng: -76.6122 }, // Baltimore, MD (Middle River)
            "21221": { lat: 39.2904, lng: -76.6122 }, // Baltimore, MD (Essex)
            "21222": { lat: 39.2904, lng: -76.6122 }, // Baltimore, MD (Dundalk)
            "21223": { lat: 39.2904, lng: -76.6122 }, // Baltimore, MD (Southwest)
            "21224": { lat: 39.2904, lng: -76.6122 }, // Baltimore, MD (Highlandtown)
            "21225": { lat: 39.2904, lng: -76.6122 }, // Baltimore, MD (Brooklyn)
            "21226": { lat: 39.2904, lng: -76.6122 }, // Baltimore, MD (Curtis Bay)
            "21227": { lat: 39.2904, lng: -76.6122 }, // Baltimore, MD (Halethorpe)
            "21228": { lat: 39.2904, lng: -76.6122 }, // Baltimore, MD (Catonsville)
            "21229": { lat: 39.2904, lng: -76.6122 }, // Baltimore, MD (Southwest)
            "21230": { lat: 39.2904, lng: -76.6122 }, // Baltimore, MD (Federal Hill)
            "21231": { lat: 39.2904, lng: -76.6122 }, // Baltimore, MD (Canton)

            // Milwaukee, WI
            "53201": { lat: 43.0389, lng: -87.9065 }, // Milwaukee, WI (Downtown)
            "53202": { lat: 43.0389, lng: -87.9065 }, // Milwaukee, WI (Downtown)
            "53203": { lat: 43.0389, lng: -87.9065 }, // Milwaukee, WI (Near South Side)
            "53204": { lat: 43.0389, lng: -87.9065 }, // Milwaukee, WI (South Side)
            "53205": { lat: 43.0389, lng: -87.9065 }, // Milwaukee, WI (Near North Side)
            "53206": { lat: 43.0389, lng: -87.9065 }, // Milwaukee, WI (North Side)
            "53207": { lat: 43.0389, lng: -87.9065 }, // Milwaukee, WI (South Side)
            "53208": { lat: 43.0389, lng: -87.9065 }, // Milwaukee, WI (Near West Side)
            "53209": { lat: 43.0389, lng: -87.9065 }, // Milwaukee, WI (North Side)
            "53210": { lat: 43.0389, lng: -87.9065 }, // Milwaukee, WI (South Side)
            "53211": { lat: 43.0389, lng: -87.9065 }, // Milwaukee, WI (East Side)
            "53212": { lat: 43.0389, lng: -87.9065 }, // Milwaukee, WI (Near North Side)
            "53213": { lat: 43.0389, lng: -87.9065 }, // Milwaukee, WI (West Side)
            "53214": { lat: 43.0389, lng: -87.9065 }, // Milwaukee, WI (West Side)
            "53215": { lat: 43.0389, lng: -87.9065 }, // Milwaukee, WI (South Side)
            "53216": { lat: 43.0389, lng: -87.9065 }, // Milwaukee, WI (North Side)
            "53217": { lat: 43.0389, lng: -87.9065 }, // Milwaukee, WI (North Side)
            "53218": { lat: 43.0389, lng: -87.9065 }, // Milwaukee, WI (North Side)
            "53219": { lat: 43.0389, lng: -87.9065 }, // Milwaukee, WI (Southwest)
            "53220": { lat: 43.0389, lng: -87.9065 }, // Milwaukee, WI (Southwest)
            "53221": { lat: 43.0389, lng: -87.9065 }, // Milwaukee, WI (Southwest)
            "53222": { lat: 43.0389, lng: -87.9065 }, // Milwaukee, WI (Northwest)
            "53223": { lat: 43.0389, lng: -87.9065 }, // Milwaukee, WI (Northwest)
            "53224": { lat: 43.0389, lng: -87.9065 }, // Milwaukee, WI (North Side)
            "53225": { lat: 43.0389, lng: -87.9065 }, // Milwaukee, WI (Northwest)
            "53226": { lat: 43.0389, lng: -87.9065 }, // Milwaukee, WI (Northwest)
            "53227": { lat: 43.0389, lng: -87.9065 }, // Milwaukee, WI (Southwest)
            "53228": { lat: 43.0389, lng: -87.9065 }, // Milwaukee, WI (Southwest)
            "53233": { lat: 43.0389, lng: -87.9065 }, // Milwaukee, WI (Downtown)
            "53234": { lat: 43.0389, lng: -87.9065 }, // Milwaukee, WI (Downtown)
            "53235": { lat: 43.0389, lng: -87.9065 }, // Milwaukee, WI (Downtown)
            "53237": { lat: 43.0389, lng: -87.9065 }, // Milwaukee, WI (Downtown)
            "53244": { lat: 43.0389, lng: -87.9065 }, // Milwaukee, WI (Southwest)
            "53259": { lat: 43.0389, lng: -87.9065 }, // Milwaukee, WI (Southwest)
            "53263": { lat: 43.0389, lng: -87.9065 }, // Milwaukee, WI (Southwest)
            "53274": { lat: 43.0389, lng: -87.9065 }, // Milwaukee, WI (Downtown)
            "53278": { lat: 43.0389, lng: -87.9065 }, // Milwaukee, WI (Downtown)
            "53288": { lat: 43.0389, lng: -87.9065 }, // Milwaukee, WI (Downtown)
            "53290": { lat: 43.0389, lng: -87.9065 }, // Milwaukee, WI (Downtown)
            "53293": { lat: 43.0389, lng: -87.9065 }, // Milwaukee, WI (Downtown)
            "53295": { lat: 43.0389, lng: -87.9065 }, // Milwaukee, WI (Downtown)

            // Albuquerque, NM
            "87101": { lat: 35.0844, lng: -106.6504 }, // Albuquerque, NM (Downtown)
            "87102": { lat: 35.0844, lng: -106.6504 }, // Albuquerque, NM (Downtown)
            "87103": { lat: 35.0844, lng: -106.6504 }, // Albuquerque, NM (North Valley)
            "87104": { lat: 35.0844, lng: -106.6504 }, // Albuquerque, NM (West Side)
            "87105": { lat: 35.0844, lng: -106.6504 }, // Albuquerque, NM (South Valley)
            "87106": { lat: 35.0844, lng: -106.6504 }, // Albuquerque, NM (Northeast Heights)
            "87107": { lat: 35.0844, lng: -106.6504 }, // Albuquerque, NM (North Valley)
            "87108": { lat: 35.0844, lng: -106.6504 }, // Albuquerque, NM (Northeast Heights)
            "87109": { lat: 35.0844, lng: -106.6504 }, // Albuquerque, NM (Northeast Heights)
            "87110": { lat: 35.0844, lng: -106.6504 }, // Albuquerque, NM (Northeast Heights)
            "87111": { lat: 35.0844, lng: -106.6504 }, // Albuquerque, NM (Northeast Heights)
            "87112": { lat: 35.0844, lng: -106.6504 }, // Albuquerque, NM (Northeast Heights)
            "87113": { lat: 35.0844, lng: -106.6504 }, // Albuquerque, NM (Northwest)
            "87114": { lat: 35.0844, lng: -106.6504 }, // Albuquerque, NM (Northwest)
            "87115": { lat: 35.0844, lng: -106.6504 }, // Albuquerque, NM (Northwest)
            "87116": { lat: 35.0844, lng: -106.6504 }, // Albuquerque, NM (Northwest)
            "87117": { lat: 35.0844, lng: -106.6504 }, // Albuquerque, NM (Northwest)
            "87118": { lat: 35.0844, lng: -106.6504 }, // Albuquerque, NM (Northwest)
            "87119": { lat: 35.0844, lng: -106.6504 }, // Albuquerque, NM (West Side)
            "87120": { lat: 35.0844, lng: -106.6504 }, // Albuquerque, NM (West Side)
            "87121": { lat: 35.0844, lng: -106.6504 }, // Albuquerque, NM (West Side)
            "87122": { lat: 35.0844, lng: -106.6504 }, // Albuquerque, NM (Northeast Heights)
            "87123": { lat: 35.0844, lng: -106.6504 }, // Albuquerque, NM (Northeast Heights)
            "87124": { lat: 35.0844, lng: -106.6504 }, // Albuquerque, NM (Northwest)
            "87125": { lat: 35.0844, lng: -106.6504 }, // Albuquerque, NM (Northwest)

            // Tucson, AZ
            "85701": { lat: 32.2226, lng: -110.9747 }, // Tucson, AZ (Downtown)
            "85702": { lat: 32.2226, lng: -110.9747 }, // Tucson, AZ (East Side)
            "85703": { lat: 32.2226, lng: -110.9747 }, // Tucson, AZ (Central)
            "85704": { lat: 32.2226, lng: -110.9747 }, // Tucson, AZ (South Side)
            "85705": { lat: 32.2226, lng: -110.9747 }, // Tucson, AZ (West Side)
            "85706": { lat: 32.2226, lng: -110.9747 }, // Tucson, AZ (East Side)
            "85707": { lat: 32.2226, lng: -110.9747 }, // Tucson, AZ (South Side)
            "85708": { lat: 32.2226, lng: -110.9747 }, // Tucson, AZ (East Side)
            "85709": { lat: 32.2226, lng: -110.9747 }, // Tucson, AZ (East Side)
            "85710": { lat: 32.2226, lng: -110.9747 }, // Tucson, AZ (East Side)
            "85711": { lat: 32.2226, lng: -110.9747 }, // Tucson, AZ (East Side)
            "85712": { lat: 32.2226, lng: -110.9747 }, // Tucson, AZ (East Side)
            "85713": { lat: 32.2226, lng: -110.9747 }, // Tucson, AZ (West Side)
            "85714": { lat: 32.2226, lng: -110.9747 }, // Tucson, AZ (South Side)
            "85715": { lat: 32.2226, lng: -110.9747 }, // Tucson, AZ (Central)
            "85716": { lat: 32.2226, lng: -110.9747 }, // Tucson, AZ (Central)
            "85717": { lat: 32.2226, lng: -110.9747 }, // Tucson, AZ (East Side)
            "85718": { lat: 32.2226, lng: -110.9747 }, // Tucson, AZ (East Side)
            "85719": { lat: 32.2226, lng: -110.9747 }, // Tucson, AZ (Central)
            "85720": { lat: 32.2226, lng: -110.9747 }, // Tucson, AZ (East Side)
            "85721": { lat: 32.2226, lng: -110.9747 }, // Tucson, AZ (University)
            "85722": { lat: 32.2226, lng: -110.9747 }, // Tucson, AZ (East Side)
            "85723": { lat: 32.2226, lng: -110.9747 }, // Tucson, AZ (East Side)
            "85724": { lat: 32.2226, lng: -110.9747 }, // Tucson, AZ (University)
            "85725": { lat: 32.2226, lng: -110.9747 }, // Tucson, AZ (East Side)
            "85726": { lat: 32.2226, lng: -110.9747 }, // Tucson, AZ (East Side)
            "85728": { lat: 32.2226, lng: -110.9747 }, // Tucson, AZ (East Side)
            "85730": { lat: 32.2226, lng: -110.9747 }, // Tucson, AZ (East Side)
            "85731": { lat: 32.2226, lng: -110.9747 }, // Tucson, AZ (Central)
            "85732": { lat: 32.2226, lng: -110.9747 }, // Tucson, AZ (Northwest)
            "85733": { lat: 32.2226, lng: -110.9747 }, // Tucson, AZ (Northwest)
            "85734": { lat: 32.2226, lng: -110.9747 }, // Tucson, AZ (Northwest)
            "85735": { lat: 32.2226, lng: -110.9747 }, // Tucson, AZ (Southwest)
            "85736": { lat: 32.2226, lng: -110.9747 }, // Tucson, AZ (Northwest)
            "85737": { lat: 32.2226, lng: -110.9747 }, // Tucson, AZ (Northwest)
            "85738": { lat: 32.2226, lng: -110.9747 }, // Tucson, AZ (Northwest)
            "85739": { lat: 32.2226, lng: -110.9747 }, // Tucson, AZ (Northwest)
            "85740": { lat: 32.2226, lng: -110.9747 }, // Tucson, AZ (Northwest)
            "85741": { lat: 32.2226, lng: -110.9747 }, // Tucson, AZ (Northwest)
            "85742": { lat: 32.2226, lng: -110.9747 }, // Tucson, AZ (Northwest)
            "85743": { lat: 32.2226, lng: -110.9747 }, // Tucson, AZ (Northwest)
            "85745": { lat: 32.2226, lng: -110.9747 }, // Tucson, AZ (Southwest)
            "85746": { lat: 32.2226, lng: -110.9747 }, // Tucson, AZ (Southwest)
            "85747": { lat: 32.2226, lng: -110.9747 }, // Tucson, AZ (South Side)
            "85748": { lat: 32.2226, lng: -110.9747 }, // Tucson, AZ (East Side)
            "85749": { lat: 32.2226, lng: -110.9747 }, // Tucson, AZ (East Side)
            "85750": { lat: 32.2226, lng: -110.9747 }, // Tucson, AZ (East Side)
            "85751": { lat: 32.2226, lng: -110.9747 }, // Tucson, AZ (East Side)
            "85752": { lat: 32.2226, lng: -110.9747 }, // Tucson, AZ (East Side)
            "85754": { lat: 32.2226, lng: -110.9747 }, // Tucson, AZ (Northwest)
            "85755": { lat: 32.2226, lng: -110.9747 }, // Tucson, AZ (Northwest)
            "85756": { lat: 32.2226, lng: -110.9747 }, // Tucson, AZ (Northwest)
            "85757": { lat: 32.2226, lng: -110.9747 }, // Tucson, AZ (Northwest)
            "85775": { lat: 32.2226, lng: -110.9747 }, // Tucson, AZ (East Side)

            // Fresno, CA
            "93701": { lat: 36.7378, lng: -119.7871 }, // Fresno, CA (Downtown)
            "93702": { lat: 36.7378, lng: -119.7871 }, // Fresno, CA (Central)
            "93703": { lat: 36.7378, lng: -119.7871 }, // Fresno, CA (Central)
            "93704": { lat: 36.7378, lng: -119.7871 }, // Fresno, CA (Central)
            "93705": { lat: 36.7378, lng: -119.7871 }, // Fresno, CA (Central)
            "93706": { lat: 36.7378, lng: -119.7871 }, // Fresno, CA (Central)
            "93707": { lat: 36.7378, lng: -119.7871 }, // Fresno, CA (North)
            "93708": { lat: 36.7378, lng: -119.7871 }, // Fresno, CA (North)
            "93709": { lat: 36.7378, lng: -119.7871 }, // Fresno, CA (North)
            "93710": { lat: 36.7378, lng: -119.7871 }, // Fresno, CA (North)
            "93711": { lat: 36.7378, lng: -119.7871 }, // Fresno, CA (North)
            "93712": { lat: 36.7378, lng: -119.7871 }, // Fresno, CA (North)
            "93714": { lat: 36.7378, lng: -119.7871 }, // Fresno, CA (Central)
            "93715": { lat: 36.7378, lng: -119.7871 }, // Fresno, CA (North)
            "93716": { lat: 36.7378, lng: -119.7871 }, // Fresno, CA (North)
            "93717": { lat: 36.7378, lng: -119.7871 }, // Fresno, CA (North)
            "93718": { lat: 36.7378, lng: -119.7871 }, // Fresno, CA (North)
            "93720": { lat: 36.7378, lng: -119.7871 }, // Fresno, CA (North)
            "93721": { lat: 36.7378, lng: -119.7871 }, // Fresno, CA (Downtown)
            "93722": { lat: 36.7378, lng: -119.7871 }, // Fresno, CA (North)
            "93723": { lat: 36.7378, lng: -119.7871 }, // Fresno, CA (North)
            "93724": { lat: 36.7378, lng: -119.7871 }, // Fresno, CA (Central)
            "93725": { lat: 36.7378, lng: -119.7871 }, // Fresno, CA (Central)
            "93726": { lat: 36.7378, lng: -119.7871 }, // Fresno, CA (North)
            "93727": { lat: 36.7378, lng: -119.7871 }, // Fresno, CA (North)
            "93728": { lat: 36.7378, lng: -119.7871 }, // Fresno, CA (North)
            "93729": { lat: 36.7378, lng: -119.7871 }, // Fresno, CA (North)
            "93730": { lat: 36.7378, lng: -119.7871 }, // Fresno, CA (North)
            "93737": { lat: 36.7378, lng: -119.7871 }, // Fresno, CA (North)
            "93740": { lat: 36.7378, lng: -119.7871 }, // Fresno, CA (North)
            "93741": { lat: 36.7378, lng: -119.7871 }, // Fresno, CA (North)
            "93744": { lat: 36.7378, lng: -119.7871 }, // Fresno, CA (North)
            "93745": { lat: 36.7378, lng: -119.7871 }, // Fresno, CA (North)
            "93747": { lat: 36.7378, lng: -119.7871 }, // Fresno, CA (North)
            "93750": { lat: 36.7378, lng: -119.7871 }, // Fresno, CA (Central)
            "93755": { lat: 36.7378, lng: -119.7871 }, // Fresno, CA (Central)
            "93760": { lat: 36.7378, lng: -119.7871 }, // Fresno, CA (Central)
            "93761": { lat: 36.7378, lng: -119.7871 }, // Fresno, CA (Central)
            "93762": { lat: 36.7378, lng: -119.7871 }, // Fresno, CA (Central)
            "93764": { lat: 36.7378, lng: -119.7871 }, // Fresno, CA (Central)
            "93765": { lat: 36.7378, lng: -119.7871 }, // Fresno, CA (Central)
            "93771": { lat: 36.7378, lng: -119.7871 }, // Fresno, CA (Central)
            "93772": { lat: 36.7378, lng: -119.7871 }, // Fresno, CA (Central)
            "93773": { lat: 36.7378, lng: -119.7871 }, // Fresno, CA (Central)
            "93774": { lat: 36.7378, lng: -119.7871 }, // Fresno, CA (Central)
            "93775": { lat: 36.7378, lng: -119.7871 }, // Fresno, CA (Central)
            "93776": { lat: 36.7378, lng: -119.7871 }, // Fresno, CA (Central)
            "93777": { lat: 36.7378, lng: -119.7871 }, // Fresno, CA (Central)
            "93778": { lat: 36.7378, lng: -119.7871 }, // Fresno, CA (Central)
            "93779": { lat: 36.7378, lng: -119.7871 }, // Fresno, CA (Central)
            "93786": { lat: 36.7378, lng: -119.7871 }, // Fresno, CA (Central)
            "93790": { lat: 36.7378, lng: -119.7871 }, // Fresno, CA (Central)
            "93791": { lat: 36.7378, lng: -119.7871 }, // Fresno, CA (Central)
            "93792": { lat: 36.7378, lng: -119.7871 }, // Fresno, CA (Central)
            "93793": { lat: 36.7378, lng: -119.7871 }, // Fresno, CA (Central)
            "93794": { lat: 36.7378, lng: -119.7871 }, // Fresno, CA (Central)
            "93844": { lat: 36.7378, lng: -119.7871 }, // Fresno, CA (Central)
            "93888": { lat: 36.7378, lng: -119.7871 }, // Fresno, CA (Central)
            "93896": { lat: 36.7378, lng: -119.7871 }, // Fresno, CA (Central)

            // Sacramento, CA
            "95814": { lat: 38.5816, lng: -121.4944 }, // Sacramento, CA (Downtown)
            "95815": { lat: 38.5816, lng: -121.4944 }, // Sacramento, CA (North)
            "95816": { lat: 38.5816, lng: -121.4944 }, // Sacramento, CA (East)
            "95817": { lat: 38.5816, lng: -121.4944 }, // Sacramento, CA (East)
            "95818": { lat: 38.5816, lng: -121.4944 }, // Sacramento, CA (Central)
            "95819": { lat: 38.5816, lng: -121.4944 }, // Sacramento, CA (Central)
            "95820": { lat: 38.5816, lng: -121.4944 }, // Sacramento, CA (South)
            "95821": { lat: 38.5816, lng: -121.4944 }, // Sacramento, CA (North)
            "95822": { lat: 38.5816, lng: -121.4944 }, // Sacramento, CA (South)
            "95823": { lat: 38.5816, lng: -121.4944 }, // Sacramento, CA (South)
            "95824": { lat: 38.5816, lng: -121.4944 }, // Sacramento, CA (South)
            "95825": { lat: 38.5816, lng: -121.4944 }, // Sacramento, CA (East)
            "95826": { lat: 38.5816, lng: -121.4944 }, // Sacramento, CA (East)
            "95827": { lat: 38.5816, lng: -121.4944 }, // Sacramento, CA (South)
            "95828": { lat: 38.5816, lng: -121.4944 }, // Sacramento, CA (South)
            "95829": { lat: 38.5816, lng: -121.4944 }, // Sacramento, CA (South)
            "95830": { lat: 38.5816, lng: -121.4944 }, // Sacramento, CA (South)
            "95831": { lat: 38.5816, lng: -121.4944 }, // Sacramento, CA (South)
            "95832": { lat: 38.5816, lng: -121.4944 }, // Sacramento, CA (South)
            "95833": { lat: 38.5816, lng: -121.4944 }, // Sacramento, CA (North)
            "95834": { lat: 38.5816, lng: -121.4944 }, // Sacramento, CA (North)
            "95835": { lat: 38.5816, lng: -121.4944 }, // Sacramento, CA (North)
            "95836": { lat: 38.5816, lng: -121.4944 }, // Sacramento, CA (North)
            "95837": { lat: 38.5816, lng: -121.4944 }, // Sacramento, CA (North)
            "95838": { lat: 38.5816, lng: -121.4944 }, // Sacramento, CA (North)
            "95841": { lat: 38.5816, lng: -121.4944 }, // Sacramento, CA (Northeast)
            "95842": { lat: 38.5816, lng: -121.4944 }, // Sacramento, CA (Northeast)
            "95843": { lat: 38.5816, lng: -121.4944 }, // Sacramento, CA (North)
            "95851": { lat: 38.5816, lng: -121.4944 }, // Sacramento, CA (North)
            "95852": { lat: 38.5816, lng: -121.4944 }, // Sacramento, CA (North)
            "95853": { lat: 38.5816, lng: -121.4944 }, // Sacramento, CA (North)
            "95860": { lat: 38.5816, lng: -121.4944 }, // Sacramento, CA (North)
            "95864": { lat: 38.5816, lng: -121.4944 }, // Sacramento, CA (North)
            "95865": { lat: 38.5816, lng: -121.4944 }, // Sacramento, CA (North)
            "95866": { lat: 38.5816, lng: -121.4944 }, // Sacramento, CA (North)
            "95867": { lat: 38.5816, lng: -121.4944 }, // Sacramento, CA (North)
            "95894": { lat: 38.5816, lng: -121.4944 }, // Sacramento, CA (Central)
            "95899": { lat: 38.5816, lng: -121.4944 }, // Sacramento, CA (Central)

            // Atlanta, GA
            "30301": { lat: 33.7490, lng: -84.3880 }, // Atlanta, GA (Downtown)
            "30302": { lat: 33.7490, lng: -84.3880 }, // Atlanta, GA (Downtown)
            "30303": { lat: 33.7490, lng: -84.3880 }, // Atlanta, GA (Downtown)
            "30304": { lat: 33.7490, lng: -84.3880 }, // Atlanta, GA (Downtown)
            "30305": { lat: 33.7490, lng: -84.3880 }, // Atlanta, GA (Buckhead)
            "30306": { lat: 33.7490, lng: -84.3880 }, // Atlanta, GA (Virginia-Highland)
            "30307": { lat: 33.7490, lng: -84.3880 }, // Atlanta, GA (Little Five Points)
            "30308": { lat: 33.7490, lng: -84.3880 }, // Atlanta, GA (Midtown)
            "30309": { lat: 33.7490, lng: -84.3880 }, // Atlanta, GA (Midtown)
            "30310": { lat: 33.7490, lng: -84.3880 }, // Atlanta, GA (West End)
            "30311": { lat: 33.7490, lng: -84.3880 }, // Atlanta, GA (Southwest)
            "30312": { lat: 33.7490, lng: -84.3880 }, // Atlanta, GA (Downtown)
            "30313": { lat: 33.7490, lng: -84.3880 }, // Atlanta, GA (Downtown)
            "30314": { lat: 33.7490, lng: -84.3880 }, // Atlanta, GA (Northwest)
            "30315": { lat: 33.7490, lng: -84.3880 }, // Atlanta, GA (South)
            "30316": { lat: 33.7490, lng: -84.3880 }, // Atlanta, GA (East Atlanta)
            "30317": { lat: 33.7490, lng: -84.3880 }, // Atlanta, GA (Decatur)
            "30318": { lat: 33.7490, lng: -84.3880 }, // Atlanta, GA (Northwest)
            "30319": { lat: 33.7490, lng: -84.3880 }, // Atlanta, GA (Buckhead)
            "30320": { lat: 33.7490, lng: -84.3880 }, // Atlanta, GA (Decatur)
            "30321": { lat: 33.7490, lng: -84.3880 }, // Atlanta, GA (Northwest)
            "30322": { lat: 33.7490, lng: -84.3880 }, // Atlanta, GA (Emory)
            "30324": { lat: 33.7490, lng: -84.3880 }, // Atlanta, GA (Buckhead)
            "30325": { lat: 33.7490, lng: -84.3880 }, // Atlanta, GA (Buckhead)
            "30326": { lat: 33.7490, lng: -84.3880 }, // Atlanta, GA (Buckhead)
            "30327": { lat: 33.7490, lng: -84.3880 }, // Atlanta, GA (Buckhead)
            "30328": { lat: 33.7490, lng: -84.3880 }, // Atlanta, GA (Sandy Springs)
            "30329": { lat: 33.7490, lng: -84.3880 }, // Atlanta, GA (Northeast)
            "30331": { lat: 33.7490, lng: -84.3880 }, // Atlanta, GA (Southwest)
            "30332": { lat: 33.7490, lng: -84.3880 }, // Atlanta, GA (Georgia Tech)
            "30334": { lat: 33.7490, lng: -84.3880 }, // Atlanta, GA (Downtown)
            "30336": { lat: 33.7490, lng: -84.3880 }, // Atlanta, GA (Southwest)
            "30337": { lat: 33.7490, lng: -84.3880 }, // Atlanta, GA (College Park)
            "30338": { lat: 33.7490, lng: -84.3880 }, // Atlanta, GA (Dunwoody)
            "30339": { lat: 33.7490, lng: -84.3880 }, // Atlanta, GA (Vinings)
            "30340": { lat: 33.7490, lng: -84.3880 }, // Atlanta, GA (Doraville)
            "30341": { lat: 33.7490, lng: -84.3880 }, // Atlanta, GA (Doraville)
            "30342": { lat: 33.7490, lng: -84.3880 }, // Atlanta, GA (Buckhead)
            "30344": { lat: 33.7490, lng: -84.3880 }, // Atlanta, GA (East Point)
            "30345": { lat: 33.7490, lng: -84.3880 }, // Atlanta, GA (Chamblee)
            "30346": { lat: 33.7490, lng: -84.3880 }, // Atlanta, GA (Dunwoody)
            "30347": { lat: 33.7490, lng: -84.3880 }, // Atlanta, GA (Decatur)
            "30348": { lat: 33.7490, lng: -84.3880 }, // Atlanta, GA (East Point)
            "30349": { lat: 33.7490, lng: -84.3880 }, // Atlanta, GA (College Park)
            "30350": { lat: 33.7490, lng: -84.3880 }, // Atlanta, GA (Dunwoody)
            "30353": { lat: 33.7490, lng: -84.3880 }, // Atlanta, GA (Downtown)
            "30354": { lat: 33.7490, lng: -84.3880 }, // Atlanta, GA (Downtown)
            "30355": { lat: 33.7490, lng: -84.3880 }, // Atlanta, GA (Sandy Springs)
            "30356": { lat: 33.7490, lng: -84.3880 }, // Atlanta, GA (Downtown)
            "30357": { lat: 33.7490, lng: -84.3880 }, // Atlanta, GA (Buckhead)
            "30358": { lat: 33.7490, lng: -84.3880 }, // Atlanta, GA (Buckhead)
            "30359": { lat: 33.7490, lng: -84.3880 }, // Atlanta, GA (Sandy Springs)
            "30360": { lat: 33.7490, lng: -84.3880 }, // Atlanta, GA (Dunwoody)
            "30361": { lat: 33.7490, lng: -84.3880 }, // Atlanta, GA (Buckhead)
            "30362": { lat: 33.7490, lng: -84.3880 }, // Atlanta, GA (Buckhead)
            "30363": { lat: 33.7490, lng: -84.3880 }, // Atlanta, GA (Downtown)
            "30364": { lat: 33.7490, lng: -84.3880 }, // Atlanta, GA (Downtown)
            "30366": { lat: 33.7490, lng: -84.3880 }, // Atlanta, GA (Dunwoody)
            "30368": { lat: 33.7490, lng: -84.3880 }, // Atlanta, GA (Downtown)
            "30369": { lat: 33.7490, lng: -84.3880 }, // Atlanta, GA (Downtown)
            "30370": { lat: 33.7490, lng: -84.3880 }, // Atlanta, GA (East Point)
            "30371": { lat: 33.7490, lng: -84.3880 }, // Atlanta, GA (Decatur)
            "30374": { lat: 33.7490, lng: -84.3880 }, // Atlanta, GA (Forest Park)
            "30375": { lat: 33.7490, lng: -84.3880 }, // Atlanta, GA (Forest Park)
            "30377": { lat: 33.7490, lng: -84.3880 }, // Atlanta, GA (Decatur)
            "30378": { lat: 33.7490, lng: -84.3880 }, // Atlanta, GA (Downtown)
            "30380": { lat: 33.7490, lng: -84.3880 }, // Atlanta, GA (Doraville)
            "30384": { lat: 33.7490, lng: -84.3880 }, // Atlanta, GA (Decatur)
            "30385": { lat: 33.7490, lng: -84.3880 }, // Atlanta, GA (Decatur)
            "30388": { lat: 33.7490, lng: -84.3880 }, // Atlanta, GA (Hapeville)
            "30392": { lat: 33.7490, lng: -84.3880 }, // Atlanta, GA (Downtown)
            "30394": { lat: 33.7490, lng: -84.3880 }, // Atlanta, GA (Downtown)
            "30396": { lat: 33.7490, lng: -84.3880 }, // Atlanta, GA (Downtown)
            "30398": { lat: 33.7490, lng: -84.3880 }, // Atlanta, GA (Downtown)
            "30399": { lat: 33.7490, lng: -84.3880 }, // Atlanta, GA (Downtown)

            // Mesa, AZ
            "85201": { lat: 33.4152, lng: -111.8315 }, // Mesa, AZ (Central)
            "85202": { lat: 33.4152, lng: -111.8315 }, // Mesa, AZ (Central)
            "85203": { lat: 33.4152, lng: -111.8315 }, // Mesa, AZ (Central)
            "85204": { lat: 33.4152, lng: -111.8315 }, // Mesa, AZ (Central)
            "85205": { lat: 33.4152, lng: -111.8315 }, // Mesa, AZ (Central)
            "85206": { lat: 33.4152, lng: -111.8315 }, // Mesa, AZ (East)
            "85207": { lat: 33.4152, lng: -111.8315 }, // Mesa, AZ (East)
            "85208": { lat: 33.4152, lng: -111.8315 }, // Mesa, AZ (East)
            "85209": { lat: 33.4152, lng: -111.8315 }, // Mesa, AZ (East)
            "85210": { lat: 33.4152, lng: -111.8315 }, // Mesa, AZ (East)
            "85211": { lat: 33.4152, lng: -111.8315 }, // Mesa, AZ (East)
            "85212": { lat: 33.4152, lng: -111.8315 }, // Mesa, AZ (East)
            "85213": { lat: 33.4152, lng: -111.8315 }, // Mesa, AZ (East)
            "85214": { lat: 33.4152, lng: -111.8315 }, // Mesa, AZ (East)
            "85215": { lat: 33.4152, lng: -111.8315 }, // Mesa, AZ (East)
            "85216": { lat: 33.4152, lng: -111.8315 }, // Mesa, AZ (East)
            "85224": { lat: 33.4152, lng: -111.8315 }, // Mesa, AZ (East)
            "85233": { lat: 33.4152, lng: -111.8315 }, // Mesa, AZ (East)
            "85234": { lat: 33.4152, lng: -111.8315 }, // Mesa, AZ (East)
            "85236": { lat: 33.4152, lng: -111.8315 }, // Mesa, AZ (East)
            "85244": { lat: 33.4152, lng: -111.8315 }, // Mesa, AZ (East)
            "85248": { lat: 33.4152, lng: -111.8315 }, // Mesa, AZ (East)
            "85249": { lat: 33.4152, lng: -111.8315 }, // Mesa, AZ (East)
            "85250": { lat: 33.4152, lng: -111.8315 }, // Mesa, AZ (East)
            "85251": { lat: 33.4152, lng: -111.8315 }, // Mesa, AZ (East)
            "85252": { lat: 33.4152, lng: -111.8315 }, // Mesa, AZ (East)
            "85253": { lat: 33.4152, lng: -111.8315 }, // Mesa, AZ (East)
            "85254": { lat: 33.4152, lng: -111.8315 }, // Mesa, AZ (East)
            "85255": { lat: 33.4152, lng: -111.8315 }, // Mesa, AZ (East)
            "85256": { lat: 33.4152, lng: -111.8315 }, // Mesa, AZ (East)
            "85257": { lat: 33.4152, lng: -111.8315 }, // Mesa, AZ (East)
            "85258": { lat: 33.4152, lng: -111.8315 }, // Mesa, AZ (East)
            "85259": { lat: 33.4152, lng: -111.8315 }, // Mesa, AZ (East)
            "85260": { lat: 33.4152, lng: -111.8315 }, // Mesa, AZ (East)
            "85261": { lat: 33.4152, lng: -111.8315 }, // Mesa, AZ (East)
            "85262": { lat: 33.4152, lng: -111.8315 }, // Mesa, AZ (East)
            "85263": { lat: 33.4152, lng: -111.8315 }, // Mesa, AZ (East)
            "85264": { lat: 33.4152, lng: -111.8315 }, // Mesa, AZ (East)
            "85266": { lat: 33.4152, lng: -111.8315 }, // Mesa, AZ (East)
            "85267": { lat: 33.4152, lng: -111.8315 }, // Mesa, AZ (East)
            "85268": { lat: 33.4152, lng: -111.8315 }, // Mesa, AZ (East)
            "85269": { lat: 33.4152, lng: -111.8315 }, // Mesa, AZ (East)
            "85274": { lat: 33.4152, lng: -111.8315 }, // Mesa, AZ (East)
            "85275": { lat: 33.4152, lng: -111.8315 }, // Mesa, AZ (East)
            "85277": { lat: 33.4152, lng: -111.8315 }, // Mesa, AZ (East)
            "85295": { lat: 33.4152, lng: -111.8315 }, // Mesa, AZ (East)
            "85296": { lat: 33.4152, lng: -111.8315 }, // Mesa, AZ (East)
            "85297": { lat: 33.4152, lng: -111.8315 }, // Mesa, AZ (East)
            "85298": { lat: 33.4152, lng: -111.8315 }, // Mesa, AZ (East)
            "85299": { lat: 33.4152, lng: -111.8315 }, // Mesa, AZ (East)

            // Kansas City, MO
            "64101": { lat: 39.1366, lng: -94.5688 }, // Kansas City, MO
            "64102": { lat: 39.1366, lng: -94.5688 }, // Kansas City, MO
            "64105": { lat: 39.1366, lng: -94.5688 }, // Kansas City, MO
            "64106": { lat: 39.1366, lng: -94.5688 }, // Kansas City, MO
            "64108": { lat: 39.1366, lng: -94.5688 }, // Kansas City, MO
            "64109": { lat: 39.1366, lng: -94.5688 }, // Kansas City, MO
            "64110": { lat: 39.1366, lng: -94.5688 }, // Kansas City, MO
            "64111": { lat: 39.1366, lng: -94.5688 }, // Kansas City, MO
            "64112": { lat: 39.1366, lng: -94.5688 }, // Kansas City, MO
            "64113": { lat: 39.1366, lng: -94.5688 }, // Kansas City, MO
            "64114": { lat: 39.1366, lng: -94.5688 }, // Kansas City, MO
            "64116": { lat: 39.1366, lng: -94.5688 }, // Kansas City, MO (North Kansas City)
            "64117": { lat: 39.1366, lng: -94.5688 }, // Kansas City, MO (North Kansas City)
            "64118": { lat: 39.1366, lng: -94.5688 }, // Kansas City, MO (Gladstone)
            "64119": { lat: 39.1366, lng: -94.5688 }, // Kansas City, MO (Gladstone)
            "64120": { lat: 39.1366, lng: -94.5688 }, // Kansas City, MO
            "64123": { lat: 39.1366, lng: -94.5688 }, // Kansas City, MO
            "64124": { lat: 39.1366, lng: -94.5688 }, // Kansas City, MO
            "64125": { lat: 39.1366, lng: -94.5688 }, // Kansas City, MO
            "64126": { lat: 39.1366, lng: -94.5688 }, // Kansas City, MO
            "64127": { lat: 39.1366, lng: -94.5688 }, // Kansas City, MO
            "64128": { lat: 39.1366, lng: -94.5688 }, // Kansas City, MO
            "64129": { lat: 39.1366, lng: -94.5688 }, // Kansas City, MO (Raytown)
            "64130": { lat: 39.1366, lng: -94.5688 }, // Kansas City, MO
            "64131": { lat: 39.1366, lng: -94.5688 }, // Kansas City, MO
            "64132": { lat: 39.1366, lng: -94.5688 }, // Kansas City, MO
            "64133": { lat: 39.1366, lng: -94.5688 }, // Kansas City, MO (Raytown)
            "64134": { lat: 39.1366, lng: -94.5688 }, // Kansas City, MO
            "64136": { lat: 39.1366, lng: -94.5688 }, // Kansas City, MO
            "64137": { lat: 39.1366, lng: -94.5688 }, // Kansas City, MO
            "64138": { lat: 39.1366, lng: -94.5688 }, // Kansas City, MO (Raytown)
            "64139": { lat: 39.1366, lng: -94.5688 }, // Kansas City, MO
            "64145": { lat: 39.1366, lng: -94.5688 }, // Kansas City, MO
            "64146": { lat: 39.1366, lng: -94.5688 }, // Kansas City, MO
            "64147": { lat: 39.1366, lng: -94.5688 }, // Kansas City, MO (Martin City)
            "64149": { lat: 39.1366, lng: -94.5688 }, // Kansas City, MO
            "64150": { lat: 39.1366, lng: -94.5688 }, // Kansas City, MO (Northmoor)
            "64151": { lat: 39.1366, lng: -94.5688 }, // Kansas City, MO (Platte Woods, Riverside)
            "64152": { lat: 39.1366, lng: -94.5688 }, // Kansas City, MO (Parkville)
            "64153": { lat: 39.1366, lng: -94.5688 }, // Kansas City, MO (Weatherby Lake)
            "64154": { lat: 39.1366, lng: -94.5688 }, // Kansas City, MO
            "64155": { lat: 39.1366, lng: -94.5688 }, // Kansas City, MO
            "64156": { lat: 39.1366, lng: -94.5688 }, // Kansas City, MO
            "64157": { lat: 39.1366, lng: -94.5688 }, // Kansas City, MO
            "64158": { lat: 39.1366, lng: -94.5688 }, // Kansas City, MO
            "64161": { lat: 39.1366, lng: -94.5688 }, // Kansas City, MO (Birmingham)
            "64163": { lat: 39.1366, lng: -94.5688 }, // Kansas City, MO (Ferrelview)
            "64164": { lat: 39.1366, lng: -94.5688 }, // Kansas City, MO
            "64165": { lat: 39.1366, lng: -94.5688 }, // Kansas City, MO
            "64166": { lat: 39.1366, lng: -94.5688 }, // Kansas City, MO
            "64167": { lat: 39.1366, lng: -94.5688 }, // Kansas City, MO

            // Raleigh, NC
            "27601": { lat: 35.7737, lng: -78.6346 }, // Raleigh, NC
            "27603": { lat: 35.7737, lng: -78.6346 }, // Raleigh, NC (Mccullers)
            "27604": { lat: 35.7737, lng: -78.6346 }, // Raleigh, NC (Brentwood)
            "27605": { lat: 35.7737, lng: -78.6346 }, // Raleigh, NC (Cameron Village)
            "27606": { lat: 35.7737, lng: -78.6346 }, // Raleigh, NC
            "27607": { lat: 35.7737, lng: -78.6346 }, // Raleigh, NC (NC State University)
            "27608": { lat: 35.7737, lng: -78.6346 }, // Raleigh, NC (Five Points)
            "27609": { lat: 35.7737, lng: -78.6346 }, // Raleigh, NC (North Hills)
            "27610": { lat: 35.7737, lng: -78.6346 }, // Raleigh, NC
            "27612": { lat: 35.7737, lng: -78.6346 }, // Raleigh, NC (Crabtree Valley)
            "27613": { lat: 35.7737, lng: -78.6346 }, // Raleigh, NC
            "27614": { lat: 35.7737, lng: -78.6346 }, // Raleigh, NC (North Hills)
            "27615": { lat: 35.7737, lng: -78.6346 }, // Raleigh, NC
            "27616": { lat: 35.7737, lng: -78.6346 }, // Raleigh, NC (Brentwood)
            "27617": { lat: 35.7737, lng: -78.6346 }, // Raleigh, NC
            "27695": { lat: 35.7737, lng: -78.6346 }, // Raleigh, NC (NC State University)

            // Colorado Springs, CO
            "80902": { lat: 38.7622, lng: -104.6605 }, // Colorado Springs, CO
            "80903": { lat: 38.7622, lng: -104.6605 }, // Colorado Springs, CO
            "80904": { lat: 38.7622, lng: -104.6605 }, // Colorado Springs, CO
            "80905": { lat: 38.7622, lng: -104.6605 }, // Colorado Springs, CO
            "80906": { lat: 38.7622, lng: -104.6605 }, // Colorado Springs, CO
            "80907": { lat: 38.7622, lng: -104.6605 }, // Colorado Springs, CO
            "80908": { lat: 38.7622, lng: -104.6605 }, // Colorado Springs, CO (Black Forest)
            "80909": { lat: 38.7622, lng: -104.6605 }, // Colorado Springs, CO
            "80910": { lat: 38.7622, lng: -104.6605 }, // Colorado Springs, CO
            "80911": { lat: 38.7622, lng: -104.6605 }, // Colorado Springs, CO (Security, Widefield)
            "80913": { lat: 38.7622, lng: -104.6605 }, // Colorado Springs, CO (Fort Carson)
            "80915": { lat: 38.7622, lng: -104.6605 }, // Colorado Springs, CO
            "80916": { lat: 38.7622, lng: -104.6605 }, // Colorado Springs, CO
            "80917": { lat: 38.7622, lng: -104.6605 }, // Colorado Springs, CO
            "80918": { lat: 38.7622, lng: -104.6605 }, // Colorado Springs, CO
            "80919": { lat: 38.7622, lng: -104.6605 }, // Colorado Springs, CO (Rockrimmon)
            "80920": { lat: 38.7622, lng: -104.6605 }, // Colorado Springs, CO
            "80921": { lat: 38.7622, lng: -104.6605 }, // Colorado Springs, CO
            "80922": { lat: 38.7622, lng: -104.6605 }, // Colorado Springs, CO
            "80923": { lat: 38.7622, lng: -104.6605 }, // Colorado Springs, CO
            "80924": { lat: 38.7622, lng: -104.6605 }, // Colorado Springs, CO
            "80925": { lat: 38.7622, lng: -104.6605 }, // Colorado Springs, CO (Security)
            "80926": { lat: 38.7622, lng: -104.6605 }, // Colorado Springs, CO
            "80927": { lat: 38.7622, lng: -104.6605 }, // Colorado Springs, CO
            "80928": { lat: 38.7622, lng: -104.6605 }, // Colorado Springs, CO (Security)
            "80929": { lat: 38.7622, lng: -104.6605 }, // Colorado Springs, CO (Security)
            "80930": { lat: 38.7622, lng: -104.6605 }, // Colorado Springs, CO (Security)
            "80938": { lat: 38.7622, lng: -104.6605 }, // Colorado Springs, CO
            "80951": { lat: 38.7622, lng: -104.6605 } // Colorado Springs, CO
          };
          
          // Check if the ZIP code exists in the hardcoded mapping
          if (zipCoordinates[zipCode]) {
            const { lat, lng } = zipCoordinates[zipCode];
            window.LAT = lat;
            window.LNG = lng;
            window.ZIP_CODE = zipCode;
            zipDisplay.textContent = zipCode;
            coordsDisplay.textContent = `${lat}, ${lng}`;
            statusMessage.textContent = `Coordinates found: ${window.LAT}, ${window.LNG}`;
            statusMessage.style.color = "#333";
            return { lat, lng };
          } else {
            throw new Error("ZIP code not found in local database");
          }
        } catch (error) {
          console.error("Error geocoding ZIP code:", error);
          statusMessage.textContent = `Error looking up coordinates: ${error.message}. Using default ZIP ${window.ZIP_CODE}.`;
          statusMessage.style.color = "#d32f2f";
          // Reset input to current valid ZIP
          zipCodeInput.value = window.ZIP_CODE;
          return null;
        }
      }

      // Add event listener to ZIP code input for real-time geocoding
      zipCodeInput.addEventListener("blur", async function() {
        const newZip = this.value.trim();
        if (newZip && /^\d{5}$/.test(newZip) && newZip !== window.ZIP_CODE) {
          await window.geocodeZipCode(newZip);
        }
      });

      // Also handle Enter key on ZIP input
      zipCodeInput.addEventListener("keydown", async function(e) {
        if (e.key === "Enter") {
          const newZip = this.value.trim();
          if (newZip && /^\d{5}$/.test(newZip) && newZip !== window.ZIP_CODE) {
            await window.geocodeZipCode(newZip);
          }
        }
      });
    })();
  </script>


  <script>
    (function () {
      // ======================================================
      // User Categories Storage Helpers
      // ======================================================
      function loadUserCategories() {
        try {
          const stored = localStorage.getItem("userCategories");
          if (!stored) return {};
          return JSON.parse(stored) || {};
        } catch (err) {
          console.error("Error loading user categories:", err);
          return {};
        }
      }
      
      function saveUserCategories(userCategories) {
        try {
          localStorage.setItem("userCategories", JSON.stringify(userCategories));
          return true;
        } catch (err) {
          console.error("Error saving user categories:", err);
          return false;
        }
      }

      // ======================================================
      // User Groups Storage Helpers
      // ======================================================
      function loadUserGroups() {
        try {
          const stored = localStorage.getItem("userGroups");
          if (!stored) return [];
          return JSON.parse(stored) || [];
        } catch (err) {
          console.error("Error loading user groups:", err);
          return [];
        }
      }

      function saveUserGroups(userGroups) {
        try {
          localStorage.setItem("userGroups", JSON.stringify(userGroups));
          return true;
        } catch (err) {
          console.error("Error saving user groups:", err);
          return false;
        }
      }

      // ======================================================
      // Hidden Groups Storage Helpers
      // ======================================================
      function loadHiddenGroups() {
        try {
          const stored = localStorage.getItem("hiddenGroups");
          if (!stored) return [];
          return JSON.parse(stored) || [];
        } catch (err) {
          console.error("Error loading hidden groups:", err);
          return [];
        }
      }

      function saveHiddenGroups(hiddenGroups) {
        try {
          localStorage.setItem("hiddenGroups", JSON.stringify(hiddenGroups));
          return true;
        } catch (err) {
          console.error("Error saving hidden groups:", err);
          return false;
        }
      }

      // ======================================================
      // Delete Group Functionality
      // ======================================================
      function deleteGroup(groupKey) {
        try {
          // 1. Remove all user-defined categories in this group
          const userCategories = loadUserCategories();
          const categoriesToDelete = [];

          Object.entries(userCategories).forEach(([filename, filters]) => {
            if (filters[0] && filters[0].key === groupKey) {
              categoriesToDelete.push(filename);
            }
          });

          // Delete the user categories
          categoriesToDelete.forEach(filename => {
            delete userCategories[filename];
          });
          saveUserCategories(userCategories);

          // 2. Remove the group from user groups if it's a custom group
          const userGroups = loadUserGroups();
          const groupIndex = userGroups.indexOf(groupKey);
          if (groupIndex > -1) {
            userGroups.splice(groupIndex, 1);
            saveUserGroups(userGroups);
          }

          // 3. For built-in groups, we'll hide them by adding to a "hidden groups" list
          const hiddenGroups = loadHiddenGroups();
          if (!hiddenGroups.includes(groupKey)) {
            hiddenGroups.push(groupKey);
            saveHiddenGroups(hiddenGroups);
          }

          // 4. Refresh the UI
          initializeCategoryCheckboxes();

          // 5. Show success message
          console.log(`Group "${groupKey}" deleted successfully`);

        } catch (error) {
          console.error("Error deleting group:", error);
          alert("An error occurred while deleting the group. Please try again.");
        }
      }

      // ======================================================
      // Additional Categories
      // ======================================================
      const CATEGORIES = {
        "liquor_distributors.csv":     [{ key: "shop",     value: "alcohol" }],
        "manufacturers.csv":           [{ key: "industrial", value: "manufacturer" }],
        "hair_salons.csv":             [{ key: "shop",     value: "hairdresser" }],
        "nail_shops.csv":              [{ key: "shop",     value: "beauty" }],
        "auto_body_shops.csv":         [{ key: "shop",     value: "car_repair" }], // same as auto_mechanic
        "montessori_schools.csv":      [{ key: "amenity",  value: "school" }, { key: "school:level", value: "pre_school" }],
        "wealth_management.csv":       [{ key: "office",   value: "financial" }],
        "venture_capital.csv":         [{ key: "office",   value: "financial" }],
        "stock_brokers.csv":           [{ key: "office",   value: "financial" }],
        "medical_practices.csv":       [{ key: "amenity",  value: "clinic" }],
        "hospitals.csv":               [{ key: "amenity",  value: "hospital" }],
        "dental_practices.csv":        [{ key: "amenity",  value: "dentist" }],
        "veterinary_clinics.csv":      [{ key: "amenity",  value: "veterinary" }],
        "nursing_homes.csv":           [{ key: "amenity",  value: "nursing_home" }],
        "assisted_living.csv":         [{ key: "amenity",  value: "social_facility" }],
        "pharmaceutical_companies.csv": [{ key: "shop",    value: "pharmacy" }],
        "manufacturing_industrial.csv": [{ key: "industrial", value: "manufacturer" }],
        "auto_dealerships.csv":        [{ key: "shop",     value: "car" }],
        "banks_financial.csv":         [{ key: "amenity",  value: "bank" }],
        "accounting_firms.csv":        [{ key: "office",   value: "accountant" }],
        "insurance_agencies.csv":      [{ key: "office",   value: "insurance" }],
        "real_estate_agencies.csv":    [{ key: "office",   value: "real_estate" }],
        "educational_institutions.csv": [{ key: "amenity", value: "school" }],
        "daycare_centers.csv":         [{ key: "amenity",  value: "childcare" }],
        "nonprofits.csv":              [{ key: "office",   value: "non_profit_organization" }],
        "government_agencies.csv":     [{ key: "office",   value: "government" }],
        "retail_stores.csv":           [{ key: "shop" }], // generic shop filter
        "restaurants.csv":             [{ key: "amenity",  value: "restaurant" }],
        "hotels_motels.csv":           [{ key: "tourism",  value: "hotel" }],
        "logistics_transport.csv":     [{ key: "office",   value: "logistics" }],
        "engineering_firms.csv":       [{ key: "office",   value: "engineering" }],
        "architecture_design.csv":     [{ key: "office",   value: "architect" }],
        "consulting_firms.csv":        [{ key: "office",   value: "consulting" }],
        "marketing_agencies.csv":      [{ key: "office",   value: "marketing" }],
        "media_production.csv":        [{ key: "office",   value: "media" }],
        "telecom_voip.csv":            [{ key: "office",   value: "telecom" }],
        "energy_utility.csv":          [{ key: "office",   value: "utility" }],
        "gyms_fitness.csv":            [{ key: "leisure",  value: "fitness_centre" }],
        "salons_spas.csv":             [{ key: "shop",     value: "beauty" }],
        "agriculture_farming.csv":     [{ key: "landuse",  value: "farmland" }],
        "aerospace_aviation.csv":      [{ key: "aeroway" }],
        "coworking_spaces.csv":        [{ key: "office",   value: "coworking" }],
      };
    const ADDITIONAL_CATEGORIES = {
        "breweries.csv":          [{ key: "craft",   value: "brewery" }],
        "wineries.csv":           [{ key: "craft",   value: "winery" }],
        "distilleries.csv":       [{ key: "craft",   value: "distillery" }],
        "cafes.csv":              [{ key: "amenity", value: "cafe" }],
        "bars.csv":               [{ key: "amenity", value: "bar" }],
        "pubs.csv":               [{ key: "amenity", value: "pub" }],
        "nightclubs.csv":         [{ key: "amenity", value: "nightclub" }],
        "cinemas.csv":            [{ key: "amenity", value: "cinema" }],
        "art_galleries.csv":      [{ key: "tourism", value: "gallery" }],
        "museums.csv":            [{ key: "tourism", value: "museum" }],
        "bookstores.csv":         [{ key: "shop",    value: "books" }],
        "hardware_stores.csv":    [{ key: "shop",    value: "hardware" }],
        "electronics_stores.csv": [{ key: "shop",    value: "electronics" }],
        "furniture_stores.csv":   [{ key: "shop",    value: "furniture" }],
        "pet_stores.csv":         [{ key: "shop",    value: "pet" }],
        "bakeries.csv":           [{ key: "shop",    value: "bakery" }],
        "convenience_stores.csv": [{ key: "shop",    value: "convenience" }],
        "grocery_stores.csv":     [{ key: "shop",    value: "supermarket" }],
        "dry_cleaners.csv":       [{ key: "shop",    value: "laundry" }],
        "printing_shops.csv":     [{ key: "shop",    value: "printing" }],
        "tattoo_parlors.csv":     [{ key: "shop",    value: "tattoo" }],
        "car_rental.csv":         [{ key: "amenity", value: "car_rental" }],
        "fuel_stations.csv":      [{ key: "amenity", value: "fuel" }]
    };
      const CSV_HEADER = [
        "Name",
        "Street Number",
        "Street Name",
        "City",
        "State",
        "ZIP+4",
        "Phone Number",
        "Fax Number",
        "Email Address",
        "Website",
        "Contact Name(s)"
      ];

      // ======================================================
      // DOM Elements
      // ======================================================
      const runBtn = document.getElementById("runBtn");
      const logDiv = document.getElementById("log");
      const downloadsDiv = document.getElementById("downloads");
      const selectAllCategoriesCheckbox = document.getElementById("selectAllCategories");
      const allCategoriesContainer = document.getElementById("allCategories");
      const downloadAllBtn = document.getElementById("downloadAllBtn");

      // ZIP code and radius elements (needed for runExport)
      const zipCodeInput = document.getElementById("zipCode");
      const zipDisplay = document.getElementById("zipDisplay");

      // Header new entry container for adding groups
      const headerNewEntryContainer = document.getElementById("headerNewEntryContainer");



      
      // ======================================================
      // Build All Categories (including user-defined ones)
      // ======================================================
      function buildAllCategories() {
        return {
          ...CATEGORIES,
          ...ADDITIONAL_CATEGORIES,
          ...loadUserCategories()
        };
      }

      // ======================================================
      // Build All Groups (including user-defined ones, excluding hidden)
      // ======================================================
      function buildAllGroups() {
        // 1. Collect all built-in "key" group names:
        const builtInKeys = new Set();
        Object.values({ ...CATEGORIES, ...ADDITIONAL_CATEGORIES }).forEach(filters => {
          filters.forEach(f => {
            if (f.key) builtInKeys.add(f.key);
          });
        });
        // 2. Get any user-defined group names:
        const userGroups = loadUserGroups();
        // 3. Get hidden groups to filter out:
        const hiddenGroups = loadHiddenGroups();
        // 4. Merge built-in keys and userGroups (ensure unique), excluding hidden:
        return [
          ...Array.from(builtInKeys),
          ...userGroups.filter(g => !builtInKeys.has(g))
        ].filter(group => !hiddenGroups.includes(group));
      }
      
      // ======================================================
      // Initialize Category Checkboxes
      // ======================================================
      function initializeCategoryCheckboxes() {
        // Clear existing categories
        allCategoriesContainer.innerHTML = "";
        
        // Build all categories (including user-defined ones)
        const ALL_CATEGORIES = buildAllCategories();
        
        // Group categories by their key
        const categoriesByKey = groupCategoriesByKey(ALL_CATEGORIES);
        
        // Populate categories by key
        populateCategoriesByKey(categoriesByKey, allCategoriesContainer);
        
        // Set up event listeners for checkbox selection
        setupCheckboxEventListeners();
      }
      
      // Group categories by their key (amenity, shop, office, etc.)
      function groupCategoriesByKey(categoriesObj) {
        const groupedCategories = {};
        const hiddenGroups = loadHiddenGroups();

        Object.entries(categoriesObj).forEach(([filename, filters]) => {
          // Use the first filter's key as the group key
          const groupKey = filters[0].key;

          // Skip hidden groups
          if (hiddenGroups.includes(groupKey)) {
            return;
          }

          if (!groupedCategories[groupKey]) {
            groupedCategories[groupKey] = [];
          }

          groupedCategories[groupKey].push({
            filename,
            filters
          });
        });

        // Ensure all user-defined groups appear even if they have no categories (but not hidden ones)
        const userGroups = loadUserGroups();
        userGroups.forEach(groupKey => {
          if (!groupedCategories[groupKey] && !hiddenGroups.includes(groupKey)) {
            groupedCategories[groupKey] = [];
          }
        });

        // Sort the keys alphabetically
        return Object.fromEntries(
          Object.entries(groupedCategories).sort((a, b) => a[0].localeCompare(b[0]))
        );
      }
      
      // Populate categories grouped by key
      function populateCategoriesByKey(groupedCategories, container) {
        // Create a flex container for all key groups
        const keyGroupsContainer = document.createElement('div');
        keyGroupsContainer.className = 'key-groups-container';
        container.appendChild(keyGroupsContainer);
        
        Object.entries(groupedCategories).forEach(([key, categories]) => {
          // Create a group for this key
          const keyGroup = document.createElement('div');
          keyGroup.className = 'key-group';
          
          // Create header for this key
          const keyHeader = document.createElement('div');
          keyHeader.className = 'key-header';
          keyHeader.innerHTML = `
            <label>
              <input type="checkbox" class="select-key" data-key="${key}">
              ${key.charAt(0).toUpperCase() + key.slice(1)} (${categories.length})
            </label>
            <span class="group-delete-btn" data-key="${key}" title="Delete Group">&times;</span>
            <span class="add-new" data-key="${key}" title="Add New Category">+</span>
          `;
          keyGroup.appendChild(keyHeader);
          
          // Create container for items in this key
          const keyItems = document.createElement('div');
          keyItems.className = 'key-items';
          
          // Determine number of columns based on item count
          if (categories.length <= 9) {
            keyItems.classList.add('columns-1');
          } else if (categories.length <= 18) {
            keyItems.classList.add('columns-2');
          } else {
            keyItems.classList.add('columns-3');
          }
          
          // Add scrollbar if more than 27 items
          if (categories.length > 27) {
            keyItems.classList.add('scrollable');
          }
          
          // Sort categories alphabetically within this key
          categories.sort((a, b) => a.filename.localeCompare(b.filename));
          
          // Add each category in this key
          categories.forEach(({ filename }) => {
            const displayName = filename.replace('.csv', '').replace(/_/g, ' ');
            const item = document.createElement('div');
            item.className = 'category-item';
            item.innerHTML = `
              <label>
                <input type="checkbox" class="category-checkbox"
                       data-key="${key}"
                       data-filename="${filename}">
                <span>${displayName}</span>
              </label>
              <span class="remove-btn" title="Remove">&times;</span>
            `;
            keyItems.appendChild(item);
          });
          
          keyGroup.appendChild(keyItems);
          keyGroupsContainer.appendChild(keyGroup);
        });
      }
      

      
      // ======================================================
      // Setup Checkbox Event Listeners
      // ======================================================
      // Flag to track if main event listeners are set up
      let mainEventListenersSetup = false;

      function setupCheckboxEventListeners() {
        // Set up main event listener only once
        if (!mainEventListenersSetup) {
          selectAllCategoriesCheckbox.addEventListener('change', function() {
            const isChecked = this.checked;
            document.querySelectorAll('.category-checkbox').forEach(checkbox => {
              checkbox.checked = isChecked;
            });
            document.querySelectorAll('.select-key').forEach(checkbox => {
              checkbox.checked = isChecked;
            });
          });
          mainEventListenersSetup = true;
        }

        // Use event delegation for key group checkboxes and individual checkboxes
        // Remove any existing delegated listeners first
        allCategoriesContainer.removeEventListener('change', handleCategoryChange);
        allCategoriesContainer.addEventListener('change', handleCategoryChange);
      }

      function handleCategoryChange(e) {
        if (e.target.classList.contains('select-key')) {
          const key = e.target.dataset.key;
          const isChecked = e.target.checked;

          document.querySelectorAll(`.category-checkbox[data-key="${key}"]`).forEach(cb => {
            cb.checked = isChecked;
          });

          updateSelectAllCheckbox();
        } else if (e.target.classList.contains('category-checkbox')) {
          updateKeyGroupCheckbox(e.target.dataset.key);
          updateSelectAllCheckbox();
        }
      }
      
      // Update key group checkbox based on individual selections
      function updateKeyGroupCheckbox(key) {
        const keyCheckbox = document.querySelector(`.select-key[data-key="${key}"]`);
        const keyItems = document.querySelectorAll(`.category-checkbox[data-key="${key}"]`);
        
        const allChecked = Array.from(keyItems).every(cb => cb.checked);
        const someChecked = Array.from(keyItems).some(cb => cb.checked);
        
        keyCheckbox.checked = allChecked;
        keyCheckbox.indeterminate = someChecked && !allChecked;
      }
      
      // Update the "Select All" checkbox based on all selections
      function updateSelectAllCheckbox() {
        const allCheckboxes = document.querySelectorAll('.category-checkbox');
        const allChecked = Array.from(allCheckboxes).every(cb => cb.checked);
        const someChecked = Array.from(allCheckboxes).some(cb => cb.checked);
        
        selectAllCategoriesCheckbox.checked = allChecked;
        selectAllCategoriesCheckbox.indeterminate = someChecked && !allChecked;
      }

      // ======================================================
      // Logging Utility
      // ======================================================
      function log(message) {
        const timeStamp = new Date().toLocaleTimeString();
        logDiv.textContent += `[${timeStamp}] ${message}\n`;
        logDiv.scrollTop = logDiv.scrollHeight;
      }

      // ======================================================
      // Delay Helper
      // ======================================================
      function delay(ms) {
        return new Promise((resolve) => setTimeout(resolve, ms));
      }

      // ======================================================
      // Build Overpass QL Query for one category
      // ======================================================
      function buildOverpassQuery(filters) {
        // Build filter clauses for the Overpass query
        const filterClauses = filters.map(filter => {
          if (filter.key && filter.value) {
            return `["${filter.key}"="${filter.value}"]`;
          } else if (filter.key) {
            return `["${filter.key}"]`;
          }
          return "";
        }).join("");

        // Get the current coordinates and radius (to ensure we use the latest values)
        const currentLat = window.LAT;
        const currentLng = window.LNG;
        const currentRadius = window.RADIUS;

        // Around: RADIUS meters around LAT,LNG
        return `
[out:json][timeout:25];
(
  node${filterClauses}(around:${currentRadius},${currentLat},${currentLng});
  way${filterClauses}(around:${currentRadius},${currentLat},${currentLng});
  relation${filterClauses}(around:${currentRadius},${currentLat},${currentLng});
);
out center tags;`;
      }

      // ======================================================
      // Fetch from Overpass API
      // ======================================================
      async function fetchOverpass(query) {
        const url = "https://overpass-api.de/api/interpreter";
        const resp = await fetch(url, {
          method: "POST",
          headers: { "Content-Type": "application/x-www-form-urlencoded" },
          body: new URLSearchParams({ data: query })
        });
        if (!resp.ok) {
          throw new Error(`Overpass error: ${resp.status} ${resp.statusText}`);
        }
        return resp.json();
      }

      // ======================================================
      // Parse OSM tags into address fields
      // ======================================================
      function parseOsmTags(tags = {}) {
        // OSM address tags:
        //   addr:housenumber, addr:street, addr:city, addr:state, addr:postcode, contact:phone, contact:fax, contact:email, website
        const name = tags.name || "";
        const streetNumber = tags["addr:housenumber"] || "";
        const streetName   = tags["addr:street"] || "";
        const city         = tags["addr:city"] || "";
        const state        = tags["addr:state"] || "";
        // OSM typically doesn’t include ZIP+4; store full postcode if available
        const postcode     = tags["addr:postcode"] || "";
        const phone        = tags["contact:phone"] || tags.phone || "";
        const fax          = tags["contact:fax"] || "";
        const email        = tags["contact:email"] || "";
        const website      = tags["contact:website"] || tags.website || "";
        const contacts     = ""; // Not stored in standard OSM tags
        return {
          name,
          streetNumber,
          streetName,
          city,
          state,
          postcode,
          phone,
          fax,
          email,
          website,
          contacts
        };
      }

      // ======================================================
      // CSV escaping
      // ======================================================
      function escapeCSV(value) {
        if (value == null) return "";
        const str = value.toString();
        if (str.includes(",") || str.includes('"') || str.includes("\n")) {
          return `"${str.replace(/"/g, '""')}"`;
        }
        return str;
      }

      function generateCSVString(rows) {
        return rows.map(row => row.map(escapeCSV).join(",")).join("\r\n");
      }

      // ======================================================
      // Process a single category: fetch OSM data, build CSV rows, provide download link
      // ======================================================
      async function processCategory(filename, filters) {
        log(`→ Starting category: ${filename}`);
        const rows = [CSV_HEADER];

        const query = buildOverpassQuery(filters);
        log(`   Querying Overpass for filters: ${JSON.stringify(filters)}`);

        let data;
        try {
          data = await fetchOverpass(query);
        } catch (err) {
          log(`   [!] Overpass fetch error for ${filename}: ${err.message}`);
          return;
        }
        const elements = data.elements || [];
        log(`   Retrieved ${elements.length} elements for ${filename}`);

        for (const el of elements) {
          const tags = el.tags || {};
          const {
            name,
            streetNumber,
            streetName,
            city,
            state,
            postcode,
            phone,
            fax,
            email,
            website,
            contacts
          } = parseOsmTags(tags);
          
          // Skip entries with no name or no address information
          if (!name || (!streetNumber && !streetName && !city)) {
            continue;
          }
          
          rows.push([
            name,
            streetNumber,
            streetName,
            city,
            state,
            postcode,
            phone,
            fax,
            email,
            website,
            contacts
          ]);
        }

        // Skip creating CSV if there are no data rows (only header)
        if (rows.length <= 1) {
          log(`   [i] No valid data for ${filename} - skipping file creation`);
          return;
        }

        const csvContent = generateCSVString(rows);
        const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });
        const blobUrl = URL.createObjectURL(blob);
        const link = document.createElement("a");
        link.href = blobUrl;
        link.download = filename;
        link.textContent = `Download ${filename}`;
        link.className = "download-link";
        downloadsDiv.appendChild(link);

        log(`✔ Finished category: ${filename} (${rows.length - 1} rows)`);
      }

      // ======================================================
      // Main Runner: iterate selected categories sequentially
      // ======================================================
      async function runExport() {
        // Clear log and download links
        logDiv.textContent = "";
        downloadsDiv.innerHTML = "";
        
        // Get selected categories
        const selectedCheckboxes = document.querySelectorAll('.category-checkbox:checked');
        if (selectedCheckboxes.length === 0) {
          log("[!] No categories selected. Please select at least one category.");
          return;
        }
        
        // Always update ZIP code and coordinates before processing
        const currentZip = zipCodeInput.value.trim();
        if (currentZip !== window.ZIP_CODE) {
          if (/^\d{5}$/.test(currentZip)) {
            log(`Updating coordinates for ZIP code: ${currentZip}...`);
            const result = await window.geocodeZipCode(currentZip);
            if (!result) {
              log(`[!] Failed to geocode ZIP ${currentZip}. Using previous ZIP: ${window.ZIP_CODE}`);
              zipCodeInput.value = window.ZIP_CODE; // Reset input to valid ZIP
            } else {
              log(`✓ Successfully updated coordinates for ZIP ${window.ZIP_CODE}: ${window.LAT}, ${window.LNG}`);
            }
          } else {
            log(`[!] Invalid ZIP code format: ${currentZip}. Using previous ZIP: ${window.ZIP_CODE}`);
            zipCodeInput.value = window.ZIP_CODE; // Reset input to valid ZIP
          }
        }
        
        runBtn.disabled = true;
        downloadAllBtn.disabled = true;
        log("=== Export Started (Overpass) ===");
        
        // Get fresh merged categories including user-defined ones
        const ALL_CATEGORIES = buildAllCategories();
        
        for (const checkbox of selectedCheckboxes) {
          const filename = checkbox.dataset.filename;
          const filters = ALL_CATEGORIES[filename];
          
          try {
            await processCategory(filename, filters);
            // slight delay to avoid overloading Overpass
            await delay(1000);
          } catch (err) {
            log(`   [!] Error processing ${filename}: ${err.message}`);
          }
        }

        log("=== Export Finished. Download links are above. ===");
        runBtn.disabled = false;
        enableDownloadAllButton();
      }

      // ======================================================
      // Setup Dynamic Add/Remove Functionality
      // ======================================================
      function setupDynamicAddRemove() {
        // Delegate click on the "+" to insert a new-entry row
        allCategoriesContainer.addEventListener('click', function(e) {
          if (e.target.classList.contains('add-new')) {
            // 1. Determine which key group we're in:
            const key = e.target.dataset.key;  // e.g. "shop", "amenity", etc.
            // 2. Locate its parent .key-group container and then the .key-items container:
            const keyGroup = e.target.closest('.key-group');
            const keyItems = keyGroup.querySelector('.key-items');

            // 3. If there's already a .new-entry row, do nothing (prevent duplicates)
            if (keyItems.querySelector('.category-item.new-entry')) return;

            // 4. Create a new .category-item.new-entry DIV:
            const newItem = document.createElement('div');
            newItem.className = 'category-item new-entry';
            newItem.innerHTML = `
              <input type="text" class="new-category-input" placeholder="Enter new value">
              <span class="cancel-new" title="Cancel">&times;</span>
              <a href="#" class="ask-chatgpt-link" target="_blank" rel="noopener noreferrer">Ask ChatGPT for OSM tags</a>
            `;
            // 5. Prepend this newItem to keyItems so it appears at the top:
            keyItems.prepend(newItem);

            // 6. Grab references to the input field and the new "Ask ChatGPT" link:
            const inputField = newItem.querySelector('.new-category-input');
            const askLink = newItem.querySelector('.ask-chatgpt-link');

            // 7. Focus the input right away:
            inputField.focus();

            // 8. Define a helper function updateChatGPTLink():
            function updateChatGPTLink() {
              const businessValue = inputField.value.trim();
              let businessCategory = key;  // Always include the group's key
              // Construct the prompt text to ask ChatGPT:
              const promptText =
                `Given a business description of "${businessCategory}` +
                `${businessValue ? ' : ' + businessValue : ''}` +
                `", what is the correct Overpass API (OpenStreetMap) key and value to use when searching for that business? ` +
                `Please reply with a list of valid key:value pairs only, without including any code examples.`;
              // URL-encode the prompt:
              const encodedPrompt = encodeURIComponent(promptText);
              // Set the askLink's href accordingly:
              askLink.href = `https://chat.openai.com/?prompt=${encodedPrompt}`;
            }

            // 9. Bind inputField.addEventListener('input', updateChatGPTLink):
            inputField.addEventListener('input', updateChatGPTLink);

            // Initialize the ChatGPT link with the key only
            updateChatGPTLink();

            // 10. Handle keydown events on inputField:
            inputField.addEventListener('keydown', function(evt) {
              if (evt.key === 'Enter') {
                // a) Grab and trim the new value:
                const newValue = inputField.value.trim();
                if (!newValue) return;  // don't proceed if empty

                // b) Build a filename from newValue (underscores + .csv):
                const filename = newValue.replace(/\s+/g, '_') + '.csv';

                // c) Load userCategories from localStorage:
                const userCategories = loadUserCategories();

                // d) Prevent duplicates: if filename already exists in userCategories or built-in categories, alert:
                if (userCategories[filename] || buildAllCategories()[filename]) {
                  alert('Category already exists.');
                  return;
                }

                // e) Otherwise, save to localStorage:
                userCategories[filename] = [{ key, value: newValue }];
                saveUserCategories(userCategories);

                // f) Reinitialize the category list UI to reflect new addition:
                initializeCategoryCheckboxes();
              } else if (evt.key === 'Escape') {
                // Remove the inline new-entry row without saving
                newItem.remove();
              }
            });

            // 11. Bind click on the cancel icon (.cancel-new) to remove newItem:
            newItem.querySelector('.cancel-new').addEventListener('click', function() {
              newItem.remove();
            });
          }
        });

        // Delegate click on "×" (remove-btn) to delete that category
        allCategoriesContainer.addEventListener('click', function(e) {
          if (e.target.classList.contains('remove-btn')) {
            const itemDiv = e.target.closest('.category-item');
            const filename = itemDiv.querySelector('.category-checkbox')?.dataset.filename;
            if (filename && loadUserCategories()[filename]) {
              const userCategories = loadUserCategories();
              delete userCategories[filename];
              saveUserCategories(userCategories);
            }
            itemDiv.remove();
            updateKeyGroupCheckbox(itemDiv.querySelector('.category-checkbox')?.dataset.key);
            updateSelectAllCheckbox();
          }
        });

        // Delegate click on group delete button to delete entire group
        allCategoriesContainer.addEventListener('click', function(e) {
          if (e.target.classList.contains('group-delete-btn')) {
            const groupKey = e.target.dataset.key;
            const groupName = groupKey.charAt(0).toUpperCase() + groupKey.slice(1);

            // Show confirmation dialog
            const confirmed = confirm(
              `Are you sure you want to delete the entire "${groupName}" group?\n\n` +
              `This will remove:\n` +
              `• The group itself\n` +
              `• All custom categories in this group\n\n` +
              `Built-in categories will be preserved but the group will be hidden.\n\n` +
              `This action cannot be undone.`
            );

            if (confirmed) {
              deleteGroup(groupKey);
            }
          }
        });
      }

      // ======================================================
      // Setup Dynamic Add/Remove Groups Functionality
      // ======================================================
      function setupDynamicAddRemoveGroups() {
        // 1. Grab the "+" in the categories-header:
        const addGroupBtn = document.querySelector(".categories-header .add-group");
        // 2. Listen for clicks:
        addGroupBtn.addEventListener('click', function() {
          // a) Prevent multiple new-entry rows:
          if (headerNewEntryContainer.querySelector('.header-new-entry')) return;
          // b) Create the inline row:
          const newEntryDiv = document.createElement('div');
          newEntryDiv.className = 'header-new-entry';
          newEntryDiv.innerHTML = `
            <input type="text" class="new-group-input" placeholder="Enter new group name">
            <span class="cancel-new" title="Cancel">&times;</span>
            <a href="#" class="ask-chatgpt-link" target="_blank" rel="noopener noreferrer">Ask ChatGPT for OSM tags</a>
          `;
          headerNewEntryContainer.appendChild(newEntryDiv);
          // c) Grab references:
          const inputField = newEntryDiv.querySelector('.new-group-input');
          const askLink = newEntryDiv.querySelector('.ask-chatgpt-link');
          inputField.focus();
          // d) Build a helper to update the ChatGPT link dynamically:
          function updateGroupChatGPTLink() {
            const groupValue = inputField.value.trim();
            const promptText =
              `Given a group description of "${groupValue}", ` +
              `what is the correct Overpass API (OpenStreetMap) key and value ` +
              `to use when searching for that group? ` +
              `Please reply with a list of valid key:value pairs only, ` +
              `without including any code examples.`;
            const encodedPrompt = encodeURIComponent(promptText);
            askLink.href = `https://chat.openai.com/?prompt=${encodedPrompt}`;
          }
          inputField.addEventListener('input', updateGroupChatGPTLink);
          updateGroupChatGPTLink();
          // e) Handle Enter/Escape keys:
          inputField.addEventListener('keydown', function(evt) {
            if (evt.key === 'Enter') {
              const newGroupName = inputField.value.trim();
              if (!newGroupName) return;
              const userGroups = loadUserGroups();
              // Prevent duplicates against built-in groups or existing userGroups
              if (userGroups.includes(newGroupName) || buildAllGroups().includes(newGroupName)) {
                alert('Group already exists.');
                return;
              }
              userGroups.push(newGroupName);
              saveUserGroups(userGroups);
              // Re-render categories & groups:
              headerNewEntryContainer.innerHTML = "";
              initializeCategoryCheckboxes();
            } else if (evt.key === 'Escape') {
              newEntryDiv.remove();
            }
          });
          // f) Bind the cancel icon:
          newEntryDiv.querySelector('.cancel-new').addEventListener('click', function() {
            newEntryDiv.remove();
          });
        });
      }

      // Initialize the UI
      initializeCategoryCheckboxes();
      setupDynamicAddRemove();
      setupDynamicAddRemoveGroups();

      // ======================================================
      // Reset Categories Functionality
      // ======================================================
      const resetCategoriesLink = document.getElementById("resetCategoriesLink");
      resetCategoriesLink.addEventListener("click", function(e) {
        e.preventDefault();
        // 1. Clear stored user categories, groups, and hidden groups:
        localStorage.removeItem("userCategories");
        localStorage.removeItem("userGroups");
        localStorage.removeItem("hiddenGroups");

        // 2. Remove any header-level new-entry if present:
        const headerContainer = document.getElementById("headerNewEntryContainer");
        headerContainer.innerHTML = "";

        // 3. Remove any category-level new-entry rows:
        document.querySelectorAll(".category-item.new-entry").forEach(item => item.remove());

        // 4. Rebuild category checkboxes from scratch:
        initializeCategoryCheckboxes();
      });

      // ======================================================
      // Restore Hidden Groups Functionality
      // ======================================================
      const restoreGroupsLink = document.getElementById("restoreGroupsLink");
      restoreGroupsLink.addEventListener("click", function(e) {
        e.preventDefault();

        const hiddenGroups = loadHiddenGroups();

        if (hiddenGroups.length === 0) {
          alert("No hidden groups to restore.");
          return;
        }

        // Show confirmation dialog with list of hidden groups
        const groupsList = hiddenGroups.map(group =>
          `• ${group.charAt(0).toUpperCase() + group.slice(1)}`
        ).join('\n');

        const confirmed = confirm(
          `Restore the following hidden groups?\n\n${groupsList}\n\n` +
          `This will make them visible again in the categories list.`
        );

        if (confirmed) {
          // Clear hidden groups
          saveHiddenGroups([]);

          // Refresh the UI
          initializeCategoryCheckboxes();

          alert(`Successfully restored ${hiddenGroups.length} group(s).`);
        }
      });

      // ======================================================
      // Event Listener
      // ======================================================
      runBtn.addEventListener("click", runExport);

      // Enable download all button after export is complete
      function enableDownloadAllButton() {
        downloadAllBtn.disabled = false;
      }

      // Download all files as a single ZIP
      async function downloadAllFiles() {
        log("=== Creating ZIP file with all CSVs ===");
        const links = downloadsDiv.querySelectorAll("a.download-link");
        
        if (links.length === 0) {
          log("[!] No files available to download. Run the export first.");
          return;
        }
        
        // Create new JSZip instance
        const zip = new JSZip();
        
        // Add each CSV to the zip
        for (let i = 0; i < links.length; i++) {
          const link = links[i];
          const filename = link.download;
          log(`Adding ${filename} to ZIP (${i+1}/${links.length})`);
          
          // Fetch the blob from the object URL
          const response = await fetch(link.href);
          const blob = await response.blob();
          
          // Add file to zip
          zip.file(filename, blob);
        }
        
        // Generate the zip file
        log("Generating ZIP file...");
        const internalBlob = await zip.generateAsync({type: "blob"});

        // Wrap blob in new Blob({ type: "application/zip" }) to reduce Windows security warnings
        const zipBlob = new Blob([internalBlob], { type: "application/zip" });

        // Create download link for the zip
        const zipUrl = URL.createObjectURL(zipBlob);
        const zipLink = document.createElement("a");
        zipLink.href = zipUrl;
        zipLink.download = "business_data.zip";
        
        // Trigger download
        document.body.appendChild(zipLink);
        zipLink.click();
        document.body.removeChild(zipLink);

        // Clean up the object URL after a short delay
        setTimeout(() => URL.revokeObjectURL(zipUrl), 1000);

        log("=== ZIP file downloaded ===");
      }

      // Add event listener for download all button
      downloadAllBtn.addEventListener("click", downloadAllFiles);

      // Update radius values in the UI
      document.addEventListener('DOMContentLoaded', function() {
        document.querySelectorAll('.radius').forEach(el => {
          el.textContent = RADIUS_MILES;
        });
        document.querySelectorAll('.radius-meters').forEach(el => {
          el.textContent = RADIUS;
        });
      });
    })();

    // ======================================================
    // "Story of This Page" Popup Logic
    // ======================================================
    (function() {
      const openBtn = document.getElementById("openPageStory");
      const overlay = document.getElementById("pageStoryOverlay");
      const closeBtn = document.getElementById("closePageStory");
      const dialog = document.getElementById("pageStoryDialog");

      function openStory(e) {
        if (e) e.preventDefault();
        overlay.classList.remove("hidden-overlay");
        overlay.classList.add("visible-overlay");
        // trap focus on dialog
        dialog.focus();
      }

      function closeStory() {
        overlay.classList.remove("visible-overlay");
        overlay.classList.add("hidden-overlay");
        // Return focus to the opener button
        openBtn.focus();
      }

      openBtn.addEventListener("click", openStory);
      closeBtn.addEventListener("click", closeStory);

      // Close on pressing Esc
      document.addEventListener("keydown", function(e) {
        if (e.key === "Escape" && overlay.classList.contains("visible-overlay")) {
          closeStory();
        }
      });

      // Close when clicking outside the dialog
      overlay.addEventListener("click", function(e) {
        if (e.target === overlay) {
          closeStory();
        }
      });

      // Trap focus inside the dialog when open
      dialog.addEventListener("keydown", function(e) {
        if (e.key === "Tab") {
          const focusableElements = dialog.querySelectorAll("button, [href], input, select, textarea, [tabindex]:not([tabindex='-1'])");
          const firstElement = focusableElements[0];
          const lastElement = focusableElements[focusableElements.length - 1];

          if (e.shiftKey && document.activeElement === firstElement) {
            e.preventDefault();
            lastElement.focus();
          } else if (!e.shiftKey && document.activeElement === lastElement) {
            e.preventDefault();
            firstElement.focus();
          }
        }
      });
    })();

    // ======================================================
    // "Instructions" Popup Logic
    // ======================================================
    (function() {
      const openBtn = document.getElementById("openInstructions");
      const overlay = document.getElementById("instructionsOverlay");
      const closeBtn = document.getElementById("closeInstructions");
      const dialog = document.getElementById("instructionsDialog");
      const contentDiv = document.getElementById("instructionsContent");

      // Enhanced markdown to HTML converter
      function parseMarkdown(markdown) {
        let html = markdown;

        // First, extract and protect code blocks to prevent interference
        const codeBlocks = [];
        let codeBlockIndex = 0;

        // Handle code blocks with language specifiers and without
        html = html.replace(/```(\w+)?\n?([\s\S]*?)```/g, (match, lang, code) => {
          const placeholder = `__CODE_BLOCK_${codeBlockIndex}__`;
          // Escape HTML entities in code
          const escapedCode = code
            .replace(/&/g, '&amp;')
            .replace(/</g, '&lt;')
            .replace(/>/g, '&gt;')
            .replace(/"/g, '&quot;')
            .replace(/'/g, '&#39;');

          codeBlocks[codeBlockIndex] = `<pre><code>${escapedCode}</code></pre>`;
          codeBlockIndex++;
          return placeholder;
        });

        // Extract and protect inline code
        const inlineCodes = [];
        let inlineCodeIndex = 0;
        html = html.replace(/`([^`\n]+)`/g, (match, code) => {
          const placeholder = `__INLINE_CODE_${inlineCodeIndex}__`;
          // Escape HTML entities in inline code
          const escapedCode = code
            .replace(/&/g, '&amp;')
            .replace(/</g, '&lt;')
            .replace(/>/g, '&gt;')
            .replace(/"/g, '&quot;')
            .replace(/'/g, '&#39;');

          inlineCodes[inlineCodeIndex] = `<code>${escapedCode}</code>`;
          inlineCodeIndex++;
          return placeholder;
        });

        // Convert headers (order matters - longest first)
        html = html.replace(/^#### (.*$)/gim, '<h4>$1</h4>');
        html = html.replace(/^### (.*$)/gim, '<h3>$1</h3>');
        html = html.replace(/^## (.*$)/gim, '<h2>$1</h2>');
        html = html.replace(/^# (.*$)/gim, '<h1>$1</h1>');

        // Convert bold and italic (be careful with order)
        html = html.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
        html = html.replace(/(?<!\*)\*([^*\n]+)\*(?!\*)/g, '<em>$1</em>');

        // Convert links - handle internal anchors differently
        html = html.replace(/\[([^\]]+)\]\(([^)]+)\)/g, (match, text, url) => {
          if (url.startsWith('#')) {
            // Internal anchor link - use data attribute for custom handling
            return `<a href="${url}" class="internal-link" data-anchor="${url.substring(1)}">${text}</a>`;
          } else {
            // External link - open in new tab
            return `<a href="${url}" target="_blank" rel="noopener noreferrer">${text}</a>`;
          }
        });

        // Convert horizontal rules
        html = html.replace(/^---$/gm, '<hr>');

        // Process line by line instead of section by section
        const lines = html.split('\n');
        const processedLines = [];
        let currentList = null;
        let inParagraph = false;
        let paragraphContent = '';

        for (let i = 0; i < lines.length; i++) {
          const line = lines[i];
          const trimmed = line.trim();

          // Skip empty lines
          if (!trimmed) {
            // Close any open paragraph
            if (inParagraph && paragraphContent.trim()) {
              processedLines.push(`<p>${paragraphContent.trim()}</p>`);
              paragraphContent = '';
              inParagraph = false;
            }
            // Close any open list
            if (currentList) {
              processedLines.push(`</${currentList}>`);
              currentList = null;
            }
            continue;
          }

          // Skip if it's already HTML (headers, hr, etc.)
          if (trimmed.startsWith('<h') || trimmed.startsWith('<hr') || trimmed.includes('__CODE_BLOCK_')) {
            // Close any open elements first
            if (inParagraph && paragraphContent.trim()) {
              processedLines.push(`<p>${paragraphContent.trim()}</p>`);
              paragraphContent = '';
              inParagraph = false;
            }
            if (currentList) {
              processedLines.push(`</${currentList}>`);
              currentList = null;
            }
            processedLines.push(trimmed);
            continue;
          }

          // Handle unordered list items
          if (trimmed.match(/^[-*]\s/)) {
            // Close any open paragraph
            if (inParagraph && paragraphContent.trim()) {
              processedLines.push(`<p>${paragraphContent.trim()}</p>`);
              paragraphContent = '';
              inParagraph = false;
            }
            // Start or continue unordered list
            if (currentList !== 'ul') {
              if (currentList) processedLines.push(`</${currentList}>`);
              processedLines.push('<ul>');
              currentList = 'ul';
            }
            const itemContent = trimmed.substring(2).trim();
            processedLines.push(`<li>${itemContent}</li>`);
            continue;
          }

          // Handle ordered list items
          if (trimmed.match(/^\d+\.\s/)) {
            // Close any open paragraph
            if (inParagraph && paragraphContent.trim()) {
              processedLines.push(`<p>${paragraphContent.trim()}</p>`);
              paragraphContent = '';
              inParagraph = false;
            }
            // Start or continue ordered list
            if (currentList !== 'ol') {
              if (currentList) processedLines.push(`</${currentList}>`);
              processedLines.push('<ol>');
              currentList = 'ol';
            }
            const itemContent = trimmed.replace(/^\d+\.\s/, '').trim();
            processedLines.push(`<li>${itemContent}</li>`);
            continue;
          }

          // Regular text line
          // Close any open list
          if (currentList) {
            processedLines.push(`</${currentList}>`);
            currentList = null;
          }

          // Add to paragraph content
          if (inParagraph) {
            paragraphContent += ' ' + trimmed;
          } else {
            paragraphContent = trimmed;
            inParagraph = true;
          }
        }

        // Close any remaining open elements
        if (inParagraph && paragraphContent.trim()) {
          processedLines.push(`<p>${paragraphContent.trim()}</p>`);
        }
        if (currentList) {
          processedLines.push(`</${currentList}>`);
        }

        html = processedLines.join('\n');

        // Restore code blocks
        for (let i = 0; i < codeBlocks.length; i++) {
          html = html.replace(`__CODE_BLOCK_${i}__`, codeBlocks[i]);
        }

        // Restore inline code
        for (let i = 0; i < inlineCodes.length; i++) {
          html = html.replace(`__INLINE_CODE_${i}__`, inlineCodes[i]);
        }

        // Debug: log the final HTML for troubleshooting
        console.log('Parsed markdown HTML:', html.substring(0, 500) + '...');

        return html;
      }

      function setupInternalLinkHandlers() {
        // Find all internal anchor links within the instructions content
        const internalLinks = contentDiv.querySelectorAll('a.internal-link');

        internalLinks.forEach(link => {
          link.addEventListener('click', function(e) {
            e.preventDefault();

            const anchor = this.getAttribute('data-anchor');
            if (!anchor) return;

            // Find the target element by ID or by heading text
            let targetElement = contentDiv.querySelector(`#${anchor}`);

            if (!targetElement) {
              // If no element with ID found, try to find by heading text
              // Convert anchor to text format (replace hyphens with spaces, etc.)
              const searchText = anchor
                .replace(/-+/g, ' ')
                .replace(/\b\w/g, l => l.toUpperCase())
                .trim();

              // Look for headings that match
              const headings = contentDiv.querySelectorAll('h1, h2, h3, h4, h5, h6');
              for (const heading of headings) {
                const headingText = heading.textContent.trim();
                if (headingText.toLowerCase().includes(searchText.toLowerCase()) ||
                    searchText.toLowerCase().includes(headingText.toLowerCase())) {
                  targetElement = heading;
                  break;
                }
              }
            }

            if (targetElement) {
              // Scroll the instructions content container to the target element
              const instructionsContent = document.querySelector('.instructions-content');
              const targetOffset = targetElement.offsetTop - instructionsContent.offsetTop;

              instructionsContent.scrollTo({
                top: targetOffset - 20, // Add some padding
                behavior: 'smooth'
              });

              // Optional: highlight the target element briefly
              targetElement.style.backgroundColor = 'rgba(0, 75, 141, 0.1)';
              setTimeout(() => {
                targetElement.style.backgroundColor = '';
              }, 2000);
            } else {
              console.warn(`Target element not found for anchor: ${anchor}`);
            }
          });
        });
      }

      async function loadMarkdownContent() {
        try {
          contentDiv.innerHTML = '<div class="loading-message">Loading latest documentation from GitHub...</div>';

          // Multiple cache-busting strategies
          const timestamp = new Date().getTime();

          // Try multiple possible README filenames and branches
          const possibleUrls = [
            `https://raw.githubusercontent.com/mytech-today-now/business_search/main/README.md?t=${timestamp}`,
            `https://raw.githubusercontent.com/mytech-today-now/business_search/main/readme.md?t=${timestamp}`,
            `https://raw.githubusercontent.com/mytech-today-now/business_search/main/Readme.md?t=${timestamp}`,
            `https://raw.githubusercontent.com/mytech-today-now/business_search/master/README.md?t=${timestamp}`,
            `https://raw.githubusercontent.com/mytech-today-now/business_search/master/readme.md?t=${timestamp}`,
            `https://raw.githubusercontent.com/mytech-today-now/business_search/master/Readme.md?t=${timestamp}`
          ];

          let response;
          let lastError;
          let attemptedUrls = [];

          // Try each URL until one works
          for (const url of possibleUrls) {
            for (let attempt = 1; attempt <= 2; attempt++) {
              try {
                console.log(`Attempting (${attempt}/2): ${url}`);

                response = await fetch(url, {
                  headers: {
                    'Cache-Control': 'no-cache, no-store, must-revalidate',
                    'Pragma': 'no-cache',
                    'Expires': '0'
                  }
                });

                attemptedUrls.push(`${url} (attempt ${attempt}) - Status: ${response.status}`);

                if (response.ok) {
                  markdownText = await response.text();

                  // Validate content quality and completeness
                  if (markdownText.length > 1000 &&
                      markdownText.includes('# Business Search & Export Tool') &&
                      markdownText.includes('## Technology Stack') &&
                      markdownText.includes('myTech.Today')) {

                    console.log(`Success! Loaded ${markdownText.length} characters from: ${url}`);
                    const htmlContent = parseMarkdown(markdownText);

                    contentDiv.innerHTML = `
                      <div style="background: #d1ecf1; border: 1px solid #bee5eb; padding: 10px; margin-bottom: 20px; border-radius: 4px; color: #0c5460;">
                        <strong>✓ GitHub Raw:</strong> Documentation loaded successfully (${new Date().toLocaleString()})
                      </div>
                      ${htmlContent}
                    `;
                    setupInternalLinkHandlers();
                    return;
                  } else {
                    console.warn(`Content validation failed for ${url} - length: ${markdownText.length}`);
                    attemptedUrls.push(`${url} - Content validation failed (length: ${markdownText.length})`);
                  }
                }

                lastError = new Error(`HTTP error! status: ${response.status} for ${url}`);

              } catch (error) {
                lastError = error;
                attemptedUrls.push(`${url} (attempt ${attempt}) - Error: ${error.message}`);
                console.warn(`Failed attempt ${attempt} for ${url}:`, error.message);

                // Wait before retry
                if (attempt === 1) {
                  await new Promise(resolve => setTimeout(resolve, 1000));
                }
              }
            }
          }

          // If we get here, all URLs failed
          console.error('All documentation URLs failed:', attemptedUrls);
          console.error('Last error:', lastError);

          // Load embedded fallback documentation
          window.loadEmbeddedDocs();
        } catch (error) {
          console.error('Error in loadMarkdownContent:', error);
          window.loadEmbeddedDocs();
        }
      }

      // Fallback function to load basic embedded documentation
      window.loadEmbeddedDocs = function() {
        const basicDocs = `
          <div class="local-fallback-notice" style="background: #e3f2fd; border: 1px solid #2196f3; padding: 10px; margin-bottom: 20px; border-radius: 4px;">
            <strong>Basic Documentation:</strong> Embedded fallback version
          </div>

          <h1>Business Search & Export Tool</h1>

          <p>A powerful web-based application for generating targeted business mailing lists within specified geographic areas, developed by <a href="https://mytech.today" target="_blank" rel="noopener noreferrer">myTech.Today</a>.</p>

          <h2>Quick Start Guide</h2>

          <h3>1. Set Your Location</h3>
          <ul>
            <li>Enter your ZIP code in the input field (default: 60010 - Barrington, IL)</li>
            <li>Press Enter or click away to automatically update coordinates</li>
            <li>Verify the coordinates display shows your intended location</li>
          </ul>

          <h3>2. Configure Search Radius</h3>
          <ul>
            <li>Adjust the radius slider or input field (1-100 miles)</li>
            <li>Default is 30 miles - suitable for most regional business searches</li>
          </ul>

          <h3>3. Select Business Categories</h3>
          <ul>
            <li><strong>Individual Selection:</strong> Check specific categories like "lawyers.csv" or "medical_practices.csv"</li>
            <li><strong>Group Selection:</strong> Use group headers (e.g., "office", "amenity") to select all categories in that group</li>
            <li><strong>Select All:</strong> Use the master checkbox to select/deselect all categories at once</li>
          </ul>

          <h3>4. Add Custom Categories (Optional)</h3>
          <ul>
            <li>Click the "+" button within any group section</li>
            <li>Enter the business description (e.g., "veterinary clinic", "tax preparation")</li>
            <li>Click "Ask ChatGPT for OSM tags" for help finding the correct OpenStreetMap tags</li>
          </ul>

          <h3>5. Run Export</h3>
          <ul>
            <li>Click "Run Export" button</li>
            <li>Monitor the log area for real-time progress updates</li>
            <li>Wait for all selected categories to complete processing</li>
          </ul>

          <h3>6. Download Your Data</h3>
          <ul>
            <li><strong>Individual Files:</strong> Click individual download links for specific business categories</li>
            <li><strong>Complete Dataset:</strong> Click "Download All as ZIP" for a bundled file containing all CSVs</li>
          </ul>

          <h2>How It Works</h2>
          <p>This application uses OpenStreetMap's Overpass API to fetch business data within a specified geographic radius. The data is then parsed and exported as CSV files containing business information such as name, address, phone, email, and website.</p>

          <h2>Key Features</h2>
          <ul>
            <li><strong>No API Keys Required:</strong> Uses free OpenStreetMap data</li>
            <li><strong>Geographic Targeting:</strong> Search within specific mile radius of any US ZIP code</li>
            <li><strong>Multiple Export Formats:</strong> Individual CSV files or bundled ZIP download</li>
            <li><strong>Custom Categories:</strong> Add your own business types beyond the preset list</li>
            <li><strong>Real-time Progress:</strong> Monitor export progress with detailed logging</li>
          </ul>

          <h2>Support</h2>
          <p>For questions or support, contact:</p>
          <ul>
            <li><strong>myTech.Today</strong></li>
            <li>Email: <a href="mailto:<EMAIL>"><EMAIL></a></li>
            <li>Phone: <a href="tel:***********">(*************</a></li>
            <li>Website: <a href="https://mytech.today" target="_blank" rel="noopener noreferrer">https://mytech.today</a></li>
          </ul>
        `;

        contentDiv.innerHTML = basicDocs;
        setupInternalLinkHandlers();
      };

      function openInstructions(e) {
        if (e) e.preventDefault();
        overlay.classList.remove("hidden-overlay");
        overlay.classList.add("visible-overlay");
        // Load markdown content when opening
        loadMarkdownContent();
        // trap focus on dialog
        dialog.focus();
      }

      function closeInstructions() {
        overlay.classList.remove("visible-overlay");
        overlay.classList.add("hidden-overlay");
        // Return focus to the opener button
        openBtn.focus();
      }

      openBtn.addEventListener("click", openInstructions);
      closeBtn.addEventListener("click", closeInstructions);

      // Close on pressing Esc
      document.addEventListener("keydown", function(e) {
        if (e.key === "Escape" && overlay.classList.contains("visible-overlay")) {
          closeInstructions();
        }
      });

      // Close when clicking outside the dialog
      overlay.addEventListener("click", function(e) {
        if (e.target === overlay) {
          closeInstructions();
        }
      });

      // Trap focus inside the dialog when open
      dialog.addEventListener("keydown", function(e) {
        if (e.key === "Tab") {
          const focusableElements = dialog.querySelectorAll("button, [href], input, select, textarea, [tabindex]:not([tabindex='-1'])");
          const firstElement = focusableElements[0];
          const lastElement = focusableElements[focusableElements.length - 1];

          if (e.shiftKey && document.activeElement === firstElement) {
            e.preventDefault();
            lastElement.focus();
          } else if (!e.shiftKey && document.activeElement === lastElement) {
            e.preventDefault();
            firstElement.focus();
          }
        }
      });
    })();
  </script>
</body>
</html>